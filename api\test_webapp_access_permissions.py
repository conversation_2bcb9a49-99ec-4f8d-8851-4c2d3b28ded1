#!/usr/bin/env python3
"""
测试Web应用访问权限检查逻辑
"""
import os
import sys
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_permission_logic():
    """测试权限检查逻辑"""
    print("测试Web应用访问权限检查逻辑...")
    
    try:
        from app_factory import create_app
        app = create_app()
        
        with app.app_context():
            from services.webapp_permissions_service_extend import WebAppPermissionsServiceExtend
            
            # 测试数据
            app_id = str(uuid.uuid4())
            tenant_id = str(uuid.uuid4())
            user_id = str(uuid.uuid4())
            other_user_id = str(uuid.uuid4())
            
            print(f"测试应用ID: {app_id}")
            print(f"租户ID: {tenant_id}")
            print(f"用户ID: {user_id}")
            print(f"其他用户ID: {other_user_id}")
            
            # 1. 测试没有权限设置的情况（默认任何人可访问）
            print("\n1. 测试默认权限（无权限设置）...")
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (应该为True)")
            assert result == True, "默认权限应该允许访问"
            
            # 2. 测试公开访问权限
            print("\n2. 测试公开访问权限...")
            WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                access_mode="public",
                created_by=user_id
            )
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (应该为True)")
            assert result == True, "公开访问应该允许任何人访问"
            
            # 3. 测试外部SSO认证权限
            print("\n3. 测试外部SSO认证权限...")
            WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                access_mode="sso_verified",
                created_by=user_id
            )
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (应该为True)")
            assert result == True, "SSO认证权限应该不做检查，直接允许访问"
            
            # 4. 测试指定成员权限（用户不在列表中）
            print("\n4. 测试指定成员权限（用户不在列表中）...")
            WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                access_mode="private",
                member_ids=[other_user_id],  # 只添加其他用户
                created_by=user_id
            )
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (应该为False)")
            assert result == False, "指定成员权限应该拒绝非指定用户访问"
            
            # 5. 测试指定成员权限（用户在列表中）
            print("\n5. 测试指定成员权限（用户在列表中）...")
            WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                access_mode="private",
                member_ids=[user_id],  # 添加当前用户
                created_by=user_id
            )
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (应该为True)")
            assert result == True, "指定成员权限应该允许指定用户访问"
            
            # 6. 测试平台内所有成员权限（需要模拟租户成员关系）
            print("\n6. 测试平台内所有成员权限...")
            WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                access_mode="private_all",
                created_by=user_id
            )
            
            # 由于没有真实的租户成员数据，这个测试会返回False
            result = WebAppPermissionsServiceExtend.check_user_access_permission(
                app_id=app_id,
                tenant_id=tenant_id,
                user_id=user_id
            )
            print(f"   结果: {result} (预期为False，因为没有租户成员数据)")
            # 这里不做断言，因为需要真实的租户成员数据
            
            print("\n✅ 权限逻辑测试通过！")
            
            # 显示权限设置详情
            print("\n权限设置详情:")
            permission_data = WebAppPermissionsServiceExtend.get_app_permission_with_members(
                app_id=app_id,
                tenant_id=tenant_id
            )
            print(f"   访问模式: {permission_data['accessMode']}")
            print(f"   指定成员数量: {len(permission_data['members'])}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_api_endpoints():
    """测试API端点"""
    print("\n测试API端点...")
    
    try:
        import requests
        
        # 测试数据
        test_app_id = "test-app-" + str(uuid.uuid4())[:8]
        
        # 1. 测试获取访问模式
        print("1. 测试获取访问模式...")
        response = requests.get(f"http://localhost:5001/console/api/enterprise/webapp/app/access-mode?appId={test_app_id}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   访问模式: {data.get('accessMode', 'unknown')}")
        
        # 2. 测试权限检查
        print("2. 测试权限检查...")
        response = requests.get(f"http://localhost:5001/console/api/enterprise/webapp/permission?appId={test_app_id}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   权限检查结果: {data.get('result', 'unknown')}")
        
        print("✅ API端点测试完成")
        
    except Exception as e:
        print(f"❌ API测试失败: {str(e)}")
        print("注意: API测试需要服务器运行")


if __name__ == "__main__":
    print("=" * 60)
    print("Web应用访问权限检查测试")
    print("=" * 60)
    
    # 测试权限逻辑
    success = test_permission_logic()
    
    # 测试API端点
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 权限逻辑测试通过")
    else:
        print("❌ 权限逻辑测试失败")
    print("=" * 60)
