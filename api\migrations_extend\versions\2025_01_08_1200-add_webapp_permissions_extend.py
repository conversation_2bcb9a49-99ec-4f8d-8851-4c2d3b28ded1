"""add webapp permissions extend

Revision ID: 2025_01_08_1200_add_webapp_permissions_extend
Revises: 2025_07_02_1711-add_pricing_rules_extend
Create Date: 2025-01-08 12:00:00.000000

"""
import sqlalchemy as sa
from alembic import op

import models.types as types

# revision identifiers, used by Alembic.
revision = '2025_01_08_1200_add_webapp_permissions_extend'
down_revision = '2025_07_02_1711-add_pricing_rules_extend'
branch_labels = None
depends_on = None


def upgrade():
    """创建web应用权限相关表"""
    
    # 检查表是否已存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    existing_tables = inspector.get_table_names()
    
    # 1. 创建web应用权限设置表
    if 'webapp_permissions_extend' not in existing_tables:
        op.create_table('webapp_permissions_extend',
            sa.Column('id', types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
            sa.Column('app_id', types.StringUUID(), nullable=False),
            sa.Column('tenant_id', types.StringUUID(), nullable=False),
            sa.Column('access_mode', sa.String(length=32), nullable=False, server_default='public', 
                     comment='访问模式: public, private, private_all, sso_verified'),
            sa.Column('created_by', types.StringUUID(), nullable=False),
            sa.Column('updated_by', types.StringUUID(), nullable=True),
            sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
            sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
            sa.PrimaryKeyConstraint('id', name='webapp_permissions_extend_pkey'),
            sa.UniqueConstraint('app_id', name='unique_app_webapp_permission'),
            comment='Web应用权限设置表'
        )
        
        # 添加索引
        op.create_index('idx_webapp_permissions_extend_app_id', 'webapp_permissions_extend', ['app_id'])
        op.create_index('idx_webapp_permissions_extend_tenant_id', 'webapp_permissions_extend', ['tenant_id'])
        op.create_index('idx_webapp_permissions_extend_access_mode', 'webapp_permissions_extend', ['access_mode'])
    
    # 2. 创建web应用成员权限关联表
    if 'webapp_member_permissions_extend' not in existing_tables:
        op.create_table('webapp_member_permissions_extend',
            sa.Column('id', types.StringUUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False),
            sa.Column('webapp_permission_id', types.StringUUID(), nullable=False),
            sa.Column('account_id', types.StringUUID(), nullable=False),
            sa.Column('subject_type', sa.String(length=16), nullable=False, server_default='account',
                     comment='主体类型: account, group'),
            sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP(0)'), nullable=False),
            sa.PrimaryKeyConstraint('id', name='webapp_member_permissions_extend_pkey'),
            sa.UniqueConstraint('webapp_permission_id', 'account_id', 'subject_type', 
                              name='unique_webapp_member_permission'),
            comment='Web应用成员权限关联表'
        )
        
        # 添加索引
        op.create_index('idx_webapp_member_permissions_extend_webapp_id', 'webapp_member_permissions_extend', ['webapp_permission_id'])
        op.create_index('idx_webapp_member_permissions_extend_account_id', 'webapp_member_permissions_extend', ['account_id'])
        op.create_index('idx_webapp_member_permissions_extend_subject_type', 'webapp_member_permissions_extend', ['subject_type'])
        
        # 添加外键约束
        op.create_foreign_key(
            'fk_webapp_member_permissions_extend_webapp_permission_id',
            'webapp_member_permissions_extend', 'webapp_permissions_extend',
            ['webapp_permission_id'], ['id'],
            ondelete='CASCADE'
        )


def downgrade():
    """删除web应用权限相关表"""
    
    # 检查表是否存在
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    existing_tables = inspector.get_table_names()
    
    # 删除成员权限关联表
    if 'webapp_member_permissions_extend' in existing_tables:
        op.drop_table('webapp_member_permissions_extend')
    
    # 删除权限设置表
    if 'webapp_permissions_extend' in existing_tables:
        op.drop_table('webapp_permissions_extend')
