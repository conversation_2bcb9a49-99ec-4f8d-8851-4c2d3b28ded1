"""Web应用权限扩展模型"""
import enum
from datetime import datetime
from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from models.base import Base
from models.engine import db
from models.types import StringUUID


class WebAppAccessMode(enum.StrEnum):
    """Web应用访问模式枚举"""
    PUBLIC = "public"  # 公开访问
    PRIVATE = "private"  # 指定成员访问
    PRIVATE_ALL = "private_all"  # 组织内所有成员访问
    SSO_VERIFIED = "sso_verified"  # 外部SSO验证用户访问


class WebAppSubjectType(enum.StrEnum):
    """Web应用权限主体类型枚举"""
    ACCOUNT = "account"  # 账户
    GROUP = "group"  # 组


class WebAppPermissionExtend(Base):
    """Web应用权限设置表"""
    __tablename__ = "webapp_permissions_extend"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="webapp_permissions_extend_pkey"),
        db.UniqueConstraint("app_id", name="unique_app_webapp_permission"),
        db.Index("idx_webapp_permissions_extend_app_id", "app_id"),
        db.Index("idx_webapp_permissions_extend_tenant_id", "tenant_id"),
        db.Index("idx_webapp_permissions_extend_access_mode", "access_mode"),
    )

    id: Mapped[str] = mapped_column(StringUUID, server_default=db.text("uuid_generate_v4()"), primary_key=True)
    app_id: Mapped[str] = mapped_column(StringUUID, nullable=False, comment="应用ID")
    tenant_id: Mapped[str] = mapped_column(StringUUID, nullable=False, comment="租户ID")
    access_mode: Mapped[str] = mapped_column(
        db.String(32), 
        nullable=False, 
        server_default="public",
        comment="访问模式: public, private, private_all, sso_verified"
    )
    created_by: Mapped[str] = mapped_column(StringUUID, nullable=False, comment="创建者ID")
    updated_by: Mapped[Optional[str]] = mapped_column(StringUUID, nullable=True, comment="更新者ID")
    created_at: Mapped[datetime] = mapped_column(db.DateTime, nullable=False, server_default=func.current_timestamp())
    updated_at: Mapped[datetime] = mapped_column(db.DateTime, nullable=False, server_default=func.current_timestamp())

    # 关联关系
    member_permissions: Mapped[List["WebAppMemberPermissionExtend"]] = relationship(
        "WebAppMemberPermissionExtend",
        back_populates="webapp_permission",
        cascade="all, delete-orphan"
    )

    @property
    def access_mode_enum(self) -> WebAppAccessMode:
        """获取访问模式枚举"""
        return WebAppAccessMode(self.access_mode)

    @access_mode_enum.setter
    def access_mode_enum(self, value: WebAppAccessMode):
        """设置访问模式枚举"""
        self.access_mode = value.value

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "app_id": self.app_id,
            "tenant_id": self.tenant_id,
            "access_mode": self.access_mode,
            "created_by": self.created_by,
            "updated_by": self.updated_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "member_permissions": [mp.to_dict() for mp in self.member_permissions] if self.member_permissions else []
        }


class WebAppMemberPermissionExtend(Base):
    """Web应用成员权限关联表"""
    __tablename__ = "webapp_member_permissions_extend"
    __table_args__ = (
        db.PrimaryKeyConstraint("id", name="webapp_member_permissions_extend_pkey"),
        db.UniqueConstraint("webapp_permission_id", "account_id", "subject_type", 
                          name="unique_webapp_member_permission"),
        db.Index("idx_webapp_member_permissions_extend_webapp_id", "webapp_permission_id"),
        db.Index("idx_webapp_member_permissions_extend_account_id", "account_id"),
        db.Index("idx_webapp_member_permissions_extend_subject_type", "subject_type"),
        db.ForeignKeyConstraint(
            ["webapp_permission_id"], 
            ["webapp_permissions_extend.id"],
            name="fk_webapp_member_permissions_extend_webapp_permission_id",
            ondelete="CASCADE"
        ),
    )

    id: Mapped[str] = mapped_column(StringUUID, server_default=db.text("uuid_generate_v4()"), primary_key=True)
    webapp_permission_id: Mapped[str] = mapped_column(StringUUID, nullable=False, comment="Web应用权限ID")
    account_id: Mapped[str] = mapped_column(StringUUID, nullable=False, comment="账户ID或组ID")
    subject_type: Mapped[str] = mapped_column(
        db.String(16), 
        nullable=False, 
        server_default="account",
        comment="主体类型: account, group"
    )
    created_at: Mapped[datetime] = mapped_column(db.DateTime, nullable=False, server_default=func.current_timestamp())

    # 关联关系
    webapp_permission: Mapped["WebAppPermissionExtend"] = relationship(
        "WebAppPermissionExtend",
        back_populates="member_permissions"
    )

    @property
    def subject_type_enum(self) -> WebAppSubjectType:
        """获取主体类型枚举"""
        return WebAppSubjectType(self.subject_type)

    @subject_type_enum.setter
    def subject_type_enum(self, value: WebAppSubjectType):
        """设置主体类型枚举"""
        self.subject_type = value.value

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "webapp_permission_id": self.webapp_permission_id,
            "account_id": self.account_id,
            "subject_type": self.subject_type,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
