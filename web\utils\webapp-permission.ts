/**
 * Web应用权限工具函数
 */
import { AccessMode } from '@/models/access-control'
import type { AccessControlAccount } from '@/models/access-control'

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  hasPermission: boolean
  reason?: string
  message?: string
}

/**
 * 检查用户是否有访问权限
 * @param accessMode 访问模式
 * @param currentUser 当前用户
 * @param allowedMembers 允许访问的成员列表（仅在指定成员模式下使用）
 * @returns 权限检查结果
 */
export function checkWebAppPermission(
  accessMode: AccessMode,
  currentUser: { id: string; email: string; name: string },
  allowedMembers: AccessControlAccount[] = []
): PermissionCheckResult {
  switch (accessMode) {
    case AccessMode.PUBLIC:
      return {
        hasPermission: true,
        reason: 'public_access'
      }
    
    case AccessMode.ORGANIZATION:
      // 组织内成员访问 - 这里假设当前用户已经是组织成员
      return {
        hasPermission: true,
        reason: 'organization_member'
      }
    
    case AccessMode.SPECIFIC_GROUPS_MEMBERS:
      // 指定成员访问
      const isAllowedMember = allowedMembers.some(member => member.id === currentUser.id)
      return {
        hasPermission: isAllowedMember,
        reason: isAllowedMember ? 'specific_member' : 'not_specific_member',
        message: isAllowedMember 
          ? undefined 
          : 'You do not have permission to access this application. Please contact the administrator.'
      }
    
    case AccessMode.EXTERNAL_MEMBERS:
      // 外部SSO成员访问 - 这里需要根据实际SSO实现来判断
      return {
        hasPermission: false,
        reason: 'sso_not_implemented',
        message: 'SSO authentication is required but not implemented.'
      }
    
    default:
      return {
        hasPermission: false,
        reason: 'unknown_access_mode',
        message: 'Unknown access mode.'
      }
  }
}

/**
 * 获取权限错误消息
 * @param accessMode 访问模式
 * @param t 翻译函数
 * @returns 错误消息
 */
export function getPermissionErrorMessage(
  accessMode: AccessMode,
  t: (key: string) => string
): string {
  switch (accessMode) {
    case AccessMode.SPECIFIC_GROUPS_MEMBERS:
      return t('webapp.permission.specificMembersOnly')
    case AccessMode.EXTERNAL_MEMBERS:
      return t('webapp.permission.ssoRequired')
    default:
      return t('webapp.permission.accessDenied')
  }
}

/**
 * 权限模式显示名称映射
 */
export const ACCESS_MODE_LABELS = {
  [AccessMode.PUBLIC]: 'webapp.permission.public',
  [AccessMode.ORGANIZATION]: 'webapp.permission.organization',
  [AccessMode.SPECIFIC_GROUPS_MEMBERS]: 'webapp.permission.specificMembers',
  [AccessMode.EXTERNAL_MEMBERS]: 'webapp.permission.externalMembers'
} as const

/**
 * 获取权限模式的显示名称
 * @param accessMode 访问模式
 * @param t 翻译函数
 * @returns 显示名称
 */
export function getAccessModeLabel(
  accessMode: AccessMode,
  t: (key: string) => string
): string {
  return t(ACCESS_MODE_LABELS[accessMode] || 'webapp.permission.unknown')
}

/**
 * 验证权限设置数据
 * @param accessMode 访问模式
 * @param members 成员列表
 * @returns 验证结果
 */
export function validatePermissionSettings(
  accessMode: AccessMode,
  members: AccessControlAccount[] = []
): { isValid: boolean; error?: string } {
  if (accessMode === AccessMode.SPECIFIC_GROUPS_MEMBERS && members.length === 0) {
    return {
      isValid: false,
      error: 'At least one member must be selected for specific members access mode.'
    }
  }
  
  return { isValid: true }
}

/**
 * 格式化成员列表显示
 * @param members 成员列表
 * @param maxDisplay 最大显示数量
 * @returns 格式化的显示文本
 */
export function formatMembersDisplay(
  members: AccessControlAccount[],
  maxDisplay: number = 3
): string {
  if (members.length === 0) {
    return 'No members selected'
  }
  
  if (members.length <= maxDisplay) {
    return members.map(m => m.name).join(', ')
  }
  
  const displayMembers = members.slice(0, maxDisplay).map(m => m.name).join(', ')
  const remainingCount = members.length - maxDisplay
  return `${displayMembers} and ${remainingCount} more`
}

/**
 * 检查是否需要显示权限警告
 * @param accessMode 访问模式
 * @param members 成员列表
 * @returns 是否需要显示警告
 */
export function shouldShowPermissionWarning(
  accessMode: AccessMode,
  members: AccessControlAccount[] = []
): boolean {
  return accessMode === AccessMode.SPECIFIC_GROUPS_MEMBERS && members.length === 0
}
