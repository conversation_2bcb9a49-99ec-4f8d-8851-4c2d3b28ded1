"""
验证码功能配置模块
"""


class CaptchaConfigExtend:
    """验证码功能配置类"""
    
    # 验证码基本配置
    CAPTCHA_TTL = 60  # 验证码有效期（秒）
    CAPTCHA_LENGTH = 6  # 验证码长度
    CAPTCHA_MAX_ATTEMPTS = 3  # 最大验证次数
    CAPTCHA_RATE_LIMIT = 10  # 每分钟最大生成次数
    
    # 支持的标识符类型
    SUPPORTED_IDENTIFIER_TYPES = ["email", "ip", "session"]
    
    # Redis 键前缀
    REDIS_KEY_PREFIX = "captcha"
    REDIS_RATE_LIMIT_PREFIX = "captcha_rate"
    
    # 频率限制窗口（秒）
    RATE_LIMIT_WINDOW = 60
    
    @classmethod
    def validate_identifier_type(cls, identifier_type: str) -> bool:
        """验证标识符类型是否支持"""
        return identifier_type in cls.SUPPORTED_IDENTIFIER_TYPES 