{"version": "0.2.0", "configurations": [{"name": "Python: Flask API", "type": "debugpy", "request": "launch", "module": "flask", "env": {"FLASK_APP": "app.py", "FLASK_ENV": "development", "GEVENT_SUPPORT": "True"}, "args": ["run", "--host=0.0.0.0", "--port=5001", "--no-debugger", "--no-reload"], "jinja": true, "justMyCode": true, "cwd": "${workspaceFolder}/api", "python": "${workspaceFolder}/api/.venv/bin/python"}, {"name": "Python: <PERSON><PERSON><PERSON> (Solo)", "type": "debugpy", "request": "launch", "module": "celery", "env": {"GEVENT_SUPPORT": "True"}, "args": ["-A", "app.celery", "worker", "-P", "solo", "-c", "1", "-Q", "dataset,generation,mail,ops_trace", "--loglevel", "INFO"], "justMyCode": false, "cwd": "${workspaceFolder}/api", "python": "${workspaceFolder}/api/.venv/bin/python"}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/web/node_modules/next/dist/bin/next", "runtimeArgs": ["--inspect"], "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"action": "debugWithChrome", "killOnServerStop": true, "pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "webRoot": "${workspaceFolder}/web"}, "cwd": "${workspaceFolder}/web"}]}