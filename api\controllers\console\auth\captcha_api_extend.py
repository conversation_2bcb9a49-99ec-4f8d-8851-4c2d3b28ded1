"""
验证码API控制器
"""
import base64
import io
import random

from flask import request
from flask_restful import Resource

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from libs.helper import extract_remote_ip
from services.captcha_service_extend import CaptchaErrorExtend, CaptchaServiceExtend


def generate_captcha_image(code):
    """
    统一的验证码图片生成函数
    确保总是返回PNG格式的图片
    """
    if not PIL_AVAILABLE:
        # 如果PIL不可用，返回简单的文本格式作为后备
        return f"data:text/plain;base64,{base64.b64encode(code.encode()).decode()}"
    
    try:
        # 图片尺寸
        width, height = 120, 40
        
        # 创建图片
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 背景渐变
        for y in range(height):
            for x in range(width):
                r = random.randint(240, 255)
                g = random.randint(240, 255)
                b = random.randint(240, 255)
                draw.point((x, y), fill=(r, g, b))
        
        # 增加背景噪点
        for _ in range(200):
            x = random.randint(0, width - 1)
            y = random.randint(0, height - 1)
            draw.point((x, y), fill=(random.randint(150, 200), random.randint(150, 200), random.randint(150, 200)))
        
        # 增加背景线条
        for _ in range(8):
            x1 = random.randint(0, width)
            y1 = random.randint(0, height)
            x2 = random.randint(0, width)
            y2 = random.randint(0, height)
            color = (random.randint(150, 200), random.randint(150, 200), random.randint(150, 200))
            draw.line([(x1, y1), (x2, y2)], fill=color, width=random.randint(1, 2))
        
        # 添加弧线干扰
        for _ in range(3):
            x1 = random.randint(0, width // 2)
            y1 = random.randint(0, height // 2)
            x2 = random.randint(width // 2, width)
            y2 = random.randint(height // 2, height)
            # 确保坐标正确
            if x1 > x2:
                x1, x2 = x2, x1
            if y1 > y2:
                y1, y2 = y2, y1
            start_angle = random.randint(0, 180)
            end_angle = start_angle + random.randint(30, 180)
            color = (random.randint(120, 180), random.randint(120, 180), random.randint(120, 180))
            draw.arc([x1, y1, x2, y2], start_angle, end_angle, fill=color)
        
        # 绘制验证码文字 - 增强字体处理
        font = None
        font_size = 28  # 标准字体大小
        
        # 扩展字体路径列表，覆盖更多服务器环境
        font_paths = [
            # macOS 字体
            "/System/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/Helvetica.ttc",
            
            # Linux 常见字体
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", 
            "/usr/share/fonts/TTF/arial.ttf",
            "/usr/share/fonts/truetype/droid/DroidSans-Bold.ttf",
            "/usr/share/fonts/truetype/noto/NotoSans-Bold.ttf",
            
            # CentOS/RHEL 字体
            "/usr/share/fonts/liberation/LiberationSans-Bold.ttf",
            "/usr/share/fonts/google-noto/NotoSans-Bold.ttf",
            
            # Ubuntu 字体
            "/usr/share/fonts/truetype/ubuntu/Ubuntu-Bold.ttf",
            
            # Windows 字体（如果在Windows服务器上）
            "/Windows/Fonts/arial.ttf",
            "/Windows/Fonts/calibri.ttf",
        ]
        
        # 尝试加载字体
        for font_path in font_paths:
            try:
                font = ImageFont.truetype(font_path, font_size)
                break
            except:
                continue
        
        # 如果所有字体都加载失败，使用默认字体但增大尺寸
        if font is None:
            try:
                # 尝试使用默认字体的较大版本
                font = ImageFont.load_default(size=font_size)
            except:
                # 如果不支持size参数，使用标准默认字体
                font = ImageFont.load_default()
        
        # 计算文字位置 - 根据字体类型调整间距
        if font == ImageFont.load_default():
            # 默认字体需要更大的间距和尺寸补偿
            char_spacing = 22
            char_width = 24
            char_height = 40
            text_y_offset = 6
        else:
            # 系统字体使用标准间距
            char_spacing = 18
            char_width = 30
            char_height = 35
            text_y_offset = 5
        
        text_width = len(code) * char_spacing
        start_x = (width - text_width) // 2
        
        for i, char in enumerate(code):
            # 随机位置和角度
            x = start_x + i * char_spacing + random.randint(-3, 3)
            y = random.randint(3, text_y_offset + 5)
            
            # 随机颜色（更深的颜色）
            color = (random.randint(30, 100), random.randint(30, 100), random.randint(30, 100))
            
            # 创建单个字符的图片用于旋转
            char_img = Image.new('RGBA', (char_width, char_height), (255, 255, 255, 0))
            char_draw = ImageDraw.Draw(char_img)
            
            # 根据字体类型调整文字位置和绘制方式
            if font == ImageFont.load_default():
                # 默认字体需要特殊处理来增加视觉重量
                base_x = char_width//2 - 6
                base_y = char_height//2 - 8
                
                # 多重绘制增加字体粗细效果
                for dx in [-1, 0, 1]:
                    for dy in [-1, 0, 1]:
                        char_draw.text((base_x + dx, base_y + dy), char, font=font, fill=color)
            else:
                # 系统字体使用标准位置
                char_draw.text((5, 5), char, font=font, fill=color)
            
            # 随机旋转
            angle = random.randint(-25, 25)
            char_img = char_img.rotate(angle, expand=True)
            
            # 粘贴到主图片上
            image.paste(char_img, (x, y), char_img)
        
        # 添加前景干扰线
        for _ in range(5):
            x1 = random.randint(0, width)
            y1 = random.randint(0, height)
            x2 = random.randint(0, width)
            y2 = random.randint(0, height)
            color = (random.randint(100, 150), random.randint(100, 150), random.randint(100, 150))
            draw.line([(x1, y1), (x2, y2)], fill=color, width=1)
        
        # 完全移除模糊处理，确保在所有环境下都清晰
        # 不同版本的PIL在模糊处理上表现不一致，为避免问题直接移除
        
        # 再次添加一些噪点
        for _ in range(50):
            x = random.randint(0, width - 1)
            y = random.randint(0, height - 1)
            draw.point((x, y), fill=(random.randint(100, 180), random.randint(100, 180), random.randint(100, 180)))
        
        # 转换为base64
        buffer = io.BytesIO()
        # 使用标准PNG保存，兼容所有Pillow版本
        image.save(buffer, format='PNG')
        image_data = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{image_data}"
        
    except Exception as e:
        # 如果图片生成失败，返回简单的文本验证码
        return f"data:text/plain;base64,{base64.b64encode(code.encode()).decode()}"


class CaptchaGenerateApiExtend(Resource):
    """验证码生成API"""
    
    def post(self):
        """
        生成验证码
        
        Request Body:
        {
            "identifier": "<EMAIL>",
            "identifier_type": "email"
        }
        
        Response:
        {
            "success": true,
            "ttl": 60,
            "message": "验证码已生成",
            "image": "data:image/png;base64,..."
        }
        """
        try:
            # 获取请求参数
            data = request.get_json()
            if not data:
                raise ValueError("请求体不能为空")
            
            identifier = data.get('identifier')
            identifier_type = data.get('identifier_type', 'email')
            
            # 如果是IP类型，使用客户端IP
            if identifier_type == 'ip':
                identifier = extract_remote_ip(request)
            
            if not identifier:
                raise ValueError("标识符不能为空")
            
            # 生成验证码
            result = CaptchaServiceExtend.generate_captcha(identifier, identifier_type)
            
            # 生成验证码图片
            if result.get('success'):
                # 获取验证码（用于生成图片）
                captcha_info = CaptchaServiceExtend.get_captcha_info(identifier, identifier_type, include_code=True)
                if captcha_info and 'code' in captcha_info:
                    code = captcha_info['code']
                    # 生成验证码图片
                    image_data = generate_captcha_image(code)
                    result['image'] = image_data
                else:
                    raise ValueError("无法获取验证码信息")
            
            # 移除测试用的验证码字段（生产环境）
            if 'code' in result:
                del result['code']
            
            return result, 200
            
        except CaptchaErrorExtend as e:
            error_messages = {
                CaptchaErrorExtend.INVALID_IDENTIFIER_TYPE: "不支持的标识符类型",
                CaptchaErrorExtend.INVALID_IDENTIFIER: "无效的标识符",
                CaptchaErrorExtend.CAPTCHA_RATE_LIMITED: "请求过于频繁，请稍后再试"
            }
            message = error_messages.get(str(e), "验证码生成失败")
            return {"success": False, "message": message}, 400
            
        except ValueError as e:
            return {"success": False, "message": str(e)}, 400
            
        except Exception as e:
            return {"success": False, "message": "服务器内部错误"}, 500


class CaptchaVerifyApiExtend(Resource):
    """验证码验证API"""
    
    def post(self):
        """
        验证验证码
        
        Request Body:
        {
            "identifier": "<EMAIL>",
            "identifier_type": "email",
            "code": "123456"
        }
        
        Response:
        {
            "success": true,
            "message": "验证成功"
        }
        """
        try:
            # 获取请求参数
            data = request.get_json()
            if not data:
                raise ValueError("请求体不能为空")
            
            identifier = data.get('identifier')
            identifier_type = data.get('identifier_type', 'email')
            code = data.get('code')
            
            # 如果是IP类型，使用客户端IP
            if identifier_type == 'ip':
                identifier = extract_remote_ip(request)
            
            if not identifier:
                raise ValueError("标识符不能为空")
            
            if not code:
                raise ValueError("验证码不能为空")
            
            # 验证验证码（不自动删除，保留给登录使用）
            is_valid = CaptchaServiceExtend.verify_captcha(identifier, identifier_type, code, auto_delete=False)
            
            if is_valid:
                return {"success": True, "message": "验证成功"}, 200
            else:
                return {"success": False, "message": "验证失败"}, 400
                
        except CaptchaErrorExtend as e:
            error_messages = {
                CaptchaErrorExtend.INVALID_IDENTIFIER_TYPE: "不支持的标识符类型",
                CaptchaErrorExtend.INVALID_IDENTIFIER: "无效的标识符",
                CaptchaErrorExtend.CAPTCHA_NOT_FOUND: "验证码不存在或已过期",
                CaptchaErrorExtend.CAPTCHA_INVALID: "验证码错误",
                CaptchaErrorExtend.CAPTCHA_MAX_ATTEMPTS: "验证次数过多，请重新获取验证码"
            }
            message = error_messages.get(str(e), "验证失败")
            return {"success": False, "message": message}, 400
            
        except ValueError as e:
            return {"success": False, "message": str(e)}, 400
            
        except Exception as e:
            return {"success": False, "message": "服务器内部错误"}, 500


class CaptchaInfoApiExtend(Resource):
    """验证码信息API"""
    
    def post(self):
        """
        获取验证码信息
        
        Request Body:
        {
            "identifier": "<EMAIL>",
            "identifier_type": "email"
        }
        
        Response:
        {
            "success": true,
            "data": {
                "created_at": 1234567890,
                "attempts": 1,
                "ttl": 45,
                "max_attempts": 3
            }
        }
        """
        try:
            # 获取请求参数
            data = request.get_json()
            if not data:
                raise ValueError("请求体不能为空")
            
            identifier = data.get('identifier')
            identifier_type = data.get('identifier_type', 'email')
            
            # 如果是IP类型，使用客户端IP
            if identifier_type == 'ip':
                identifier = extract_remote_ip(request)
            
            if not identifier:
                raise ValueError("标识符不能为空")
            
            # 获取验证码信息
            info = CaptchaServiceExtend.get_captcha_info(identifier, identifier_type)
            
            if info:
                return {"success": True, "data": info}, 200
            else:
                return {"success": False, "message": "验证码不存在"}, 404
                
        except ValueError as e:
            return {"success": False, "message": str(e)}, 400
            
        except Exception as e:
            return {"success": False, "message": "服务器内部错误"}, 500


class CaptchaImageApiExtend(Resource):
    """验证码图片刷新API"""
    
    def post(self):
        """
        刷新验证码（重新生成验证码和图片）
        
        Request Body:
        {
            "identifier": "<EMAIL>",
            "identifier_type": "email"
        }
        
        Response:
        {
            "success": true,
            "image": "data:image/png;base64,...",
            "ttl": 60
        }
        """
        try:
            # 获取请求参数
            data = request.get_json()
            if not data:
                raise ValueError("请求体不能为空")
            
            identifier = data.get('identifier')
            identifier_type = data.get('identifier_type', 'email')
            
            # 如果是IP类型，使用客户端IP
            if identifier_type == 'ip':
                identifier = extract_remote_ip(request)
            
            if not identifier:
                raise ValueError("标识符不能为空")
            
            # 重新生成验证码（这会覆盖旧的验证码）
            result = CaptchaServiceExtend.generate_captcha(identifier, identifier_type)
            
            if result.get('success'):
                # 获取新生成的验证码
                captcha_info = CaptchaServiceExtend.get_captcha_info(identifier, identifier_type, include_code=True)
                if captcha_info and 'code' in captcha_info:
                    code = captcha_info['code']
                    # 生成验证码图片
                    image_data = generate_captcha_image(code)
                    return {
                        "success": True, 
                        "image": image_data,
                        "ttl": result.get('ttl', 60)
                    }, 200
                else:
                    return {"success": False, "message": "验证码生成失败"}, 500
            else:
                return {"success": False, "message": result.get('message', '验证码生成失败')}, 400
                
        except ValueError as e:
            return {"success": False, "message": str(e)}, 400
            
        except Exception as e:
            return {"success": False, "message": "服务器内部错误"}, 500 