import logging

from flask_login import current_user
from flask_restful import Resource, reqparse

from controllers.console import api
from controllers.console.wraps import account_initialization_required, setup_required
from libs.login import login_required
from services.license.jwt_validator_extend import JWTValidationErrorExtend
from services.license.license_service_extend import LicenseServiceExtend
from services.license.license_token_service_extend import LicenseTokenServiceExtend

logger = logging.getLogger(__name__)

# 二开部分：license 导入功能
class LicenseTokenApi(Resource):
    """License Token管理API"""
    
    @setup_required
    def post(self):
        """上传License Token - 支持未登录状态"""
        try:
            # 解析请求参数
            parser = reqparse.RequestParser()
            parser.add_argument('token', type=str, required=True, location='json',
                              help='JWT Token is required')
            args = parser.parse_args()
            
            token_content = args['token'].strip()
            
            if not token_content:
                return {'code': 'invalid_token', 'message': 'Token content is empty'}, 400
            
            # 检查用户登录状态 - 更安全的检查方式
            is_authenticated = False
            created_by = None  # 默认值，系统操作
            
            try:
                # 检查current_user是否存在且已认证
                if current_user and hasattr(current_user, 'is_authenticated'):
                    is_authenticated = current_user.is_authenticated
                    if is_authenticated and hasattr(current_user, 'id'):
                        # 已登录用户：验证权限（只有管理员可以上传License）
                        if hasattr(current_user, 'is_admin_or_owner') and current_user.is_admin_or_owner:
                            created_by = str(current_user.id)
                            logger.info(f"License token upload requested by authenticated admin user: {current_user.id}") # noqa: E501
                        else:
                            return {'code': 'permission_denied', 'message': 'Only admin and owner can upload license token'}, 403 # noqa: E501
                    else:
                        # 已认证但无权限信息，当作未登录处理
                        is_authenticated = False
                        
                if not is_authenticated:
                    # 未登录用户：允许导入（用于License过期后的恢复场景）
                    logger.info("License token upload requested by unauthenticated user (License recovery scenario)")
                    created_by = None  # System operation, no specific user
                    
            except Exception as auth_check_error:
                # 认证检查出错，当作未登录处理
                logger.warning(f"Error checking authentication status: {auth_check_error}, treating as unauthenticated")
                is_authenticated = False
            
            # 存储Token
            token_service = LicenseTokenServiceExtend()
            license_token = token_service.store_token(token_content, created_by)
            
            logger.info(f"License token uploaded successfully by user: {created_by}, token_id: {license_token.id}")
            
            return {
                'code': 'success',
                'message': 'License token uploaded successfully',
                'data': {
                    'token_id': str(license_token.id),
                    'tenant_id': license_token.tenant_id,
                    'subscription_plan': license_token.subscription_plan,
                    'subscription_interval': license_token.subscription_interval,
                    'expired_at': license_token.expired_at.isoformat() if license_token.expired_at else None,
                    'status': license_token.token_status
                }
            }, 200
            
        except JWTValidationErrorExtend as e:
            logger.warning(f"JWT validation failed: {str(e)}")
            return {'code': 'invalid_token', 'message': f'Token validation failed: {str(e)}'}, 400
            
        except ValueError as e:
            logger.exception("Token storage failed")
            return {'code': 'storage_failed', 'message': str(e)}, 500
            
        except Exception as e:
            logger.error(f"Unexpected error uploading license token: {str(e)}", exc_info=True)
            return {'code': 'internal_error', 'message': 'Internal server error'}, 500
    
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """获取License信息 - 需要登录"""
        try:
            # 获取当前用户的租户ID
            tenant_id = current_user.current_tenant_id
            
            if not tenant_id:
                return {'code': 'no_tenant', 'message': 'No tenant associated with current user'}, 400
            
            # 获取License信息
            license_service = LicenseServiceExtend()
            enterprise_info = license_service.get_enterprise_info()
            
            return {
                'code': 'success',
                'data': {
                    'enterprise_info': enterprise_info
                }
            }, 200
            
        except Exception as e:
            logger.error(f"Error getting license info: {str(e)}", exc_info=True)
            return {'code': 'internal_error', 'message': 'Internal server error'}, 500


# 注册API路由
api.add_resource(LicenseTokenApi, '/license/token') 