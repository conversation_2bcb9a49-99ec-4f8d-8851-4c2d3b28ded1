'use client'
import { Ri<PERSON>ockLine, RiShieldCheckLine } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import Button from './button'
import { AccessMode } from '@/models/access-control'

interface WebAppPermissionErrorProps {
  accessMode: AccessMode
  onContactAdmin?: () => void
  onRetry?: () => void
  className?: string
}

export default function WebAppPermissionError({
  accessMode,
  onContactAdmin,
  onRetry,
  className = ''
}: WebAppPermissionErrorProps) {
  const { t } = useTranslation()

  const getErrorContent = () => {
    switch (accessMode) {
      case AccessMode.SPECIFIC_GROUPS_MEMBERS:
        return {
          icon: <RiLockLine className="h-12 w-12 text-text-quaternary" />,
          title: t('webapp.permission.error.specificMembersTitle'),
          description: t('webapp.permission.error.specificMembersDescription'),
          suggestion: t('webapp.permission.error.contactAdminSuggestion')
        }
      case AccessMode.EXTERNAL_MEMBERS:
        return {
          icon: <RiShieldCheckLine className="h-12 w-12 text-text-quaternary" />,
          title: t('webapp.permission.error.ssoRequiredTitle'),
          description: t('webapp.permission.error.ssoRequiredDescription'),
          suggestion: t('webapp.permission.error.ssoSuggestion')
        }
      default:
        return {
          icon: <RiLockLine className="h-12 w-12 text-text-quaternary" />,
          title: t('webapp.permission.error.accessDeniedTitle'),
          description: t('webapp.permission.error.accessDeniedDescription'),
          suggestion: t('webapp.permission.error.contactAdminSuggestion')
        }
    }
  }

  const errorContent = getErrorContent()

  return (
    <div className={`flex flex-col items-center justify-center min-h-[400px] p-8 ${className}`}>
      <div className="flex flex-col items-center text-center max-w-md">
        {/* 图标 */}
        <div className="mb-6">
          {errorContent.icon}
        </div>

        {/* 标题 */}
        <h2 className="text-xl font-semibold text-text-primary mb-3">
          {errorContent.title}
        </h2>

        {/* 描述 */}
        <p className="text-text-secondary mb-4 leading-relaxed">
          {errorContent.description}
        </p>

        {/* 建议 */}
        <p className="text-sm text-text-tertiary mb-6">
          {errorContent.suggestion}
        </p>

        {/* 操作按钮 */}
        <div className="flex gap-3">
          {onRetry && (
            <Button
              variant="ghost"
              onClick={onRetry}
              className="min-w-[100px]"
            >
              {t('common.operation.retry')}
            </Button>
          )}
          
          {onContactAdmin && (
            <Button
              variant="primary"
              onClick={onContactAdmin}
              className="min-w-[120px]"
            >
              {t('webapp.permission.contactAdmin')}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// 简化版本的权限错误提示
export function SimpleWebAppPermissionError({
  message,
  className = ''
}: {
  message: string
  className?: string
}) {
  return (
    <div className={`flex items-center justify-center p-4 ${className}`}>
      <div className="flex items-center gap-2 text-text-tertiary">
        <RiLockLine className="h-4 w-4" />
        <span className="text-sm">{message}</span>
      </div>
    </div>
  )
}

// Hook for checking webapp permission
export function useWebAppPermissionCheck(appId: string) {
  const { t } = useTranslation()

  // 使用实际的权限检查API
  const checkPermission = async () => {
    try {
      const response = await fetch(`/console/api/enterprise/webapp/permission?appId=${appId}`)
      if (response.ok) {
        const data = await response.json()
        return data.result === true
      }
      return false
    } catch (error) {
      console.error('Permission check failed:', error)
      return false
    }
  }

  const getAccessMode = async () => {
    try {
      const response = await fetch(`/console/api/enterprise/webapp/app/access-mode?appId=${appId}`)
      if (response.ok) {
        const data = await response.json()
        return data.accessMode || AccessMode.PUBLIC
      }
      return AccessMode.PUBLIC
    } catch (error) {
      console.error('Get access mode failed:', error)
      return AccessMode.PUBLIC
    }
  }

  return {
    checkPermission,
    getAccessMode
  }
}
