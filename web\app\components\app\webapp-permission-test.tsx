'use client'
import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import Button from '../base/button'
import { AccessMode } from '@/models/access-control'
import type { AccessControlAccount } from '@/models/access-control'

interface PermissionTestProps {
  appId: string
}

export default function WebAppPermissionTest({ appId }: PermissionTestProps) {
  const { t } = useTranslation()
  const [currentAccessMode, setCurrentAccessMode] = useState<AccessMode>(AccessMode.PUBLIC)
  const [members, setMembers] = useState<AccessControlAccount[]>([])
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [searchKeyword, setSearchKeyword] = useState('')
  const [loading, setLoading] = useState(false)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
  }

  // 获取当前权限设置
  const fetchCurrentPermission = async () => {
    try {
      setLoading(true)
      addLog('获取当前权限设置...')
      
      const response = await fetch(`/console/api/enterprise/webapp/app/access-mode?appId=${appId}`)
      if (response.ok) {
        const data = await response.json()
        setCurrentAccessMode(data.accessMode || AccessMode.PUBLIC)
        addLog(`当前访问模式: ${data.accessMode}`)
        
        // 如果是指定成员模式，获取成员列表
        if (data.accessMode === AccessMode.SPECIFIC_GROUPS_MEMBERS) {
          const subjectsResponse = await fetch(`/console/api/enterprise/webapp/app/subjects?appId=${appId}`)
          if (subjectsResponse.ok) {
            const subjectsData = await subjectsResponse.json()
            setMembers(subjectsData.members || [])
            addLog(`已设置的成员数量: ${subjectsData.members?.length || 0}`)
          }
        }
      } else {
        addLog(`获取权限设置失败: ${response.status}`)
      }
    } catch (error) {
      addLog(`获取权限设置错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  // 搜索成员
  const searchMembers = async () => {
    try {
      setLoading(true)
      addLog(`搜索成员: "${searchKeyword}"`)
      
      const response = await fetch(`/console/api/enterprise/webapp/members/search?keyword=${encodeURIComponent(searchKeyword)}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setSearchResults(data.subjects || [])
        addLog(`搜索到 ${data.subjects?.length || 0} 个成员`)
      } else {
        addLog(`搜索成员失败: ${response.status}`)
      }
    } catch (error) {
      addLog(`搜索成员错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  // 更新权限设置
  const updatePermission = async (accessMode: AccessMode, selectedMembers: AccessControlAccount[] = []) => {
    try {
      setLoading(true)
      addLog(`更新权限设置为: ${accessMode}`)
      
      const payload: any = {
        appId,
        accessMode
      }
      
      if (accessMode === AccessMode.SPECIFIC_GROUPS_MEMBERS && selectedMembers.length > 0) {
        payload.subjects = selectedMembers.map(member => ({
          subjectId: member.id,
          subjectType: 'account'
        }))
        addLog(`指定成员数量: ${selectedMembers.length}`)
      }
      
      const response = await fetch('/console/api/enterprise/webapp/app/access-mode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })
      
      if (response.ok) {
        const data = await response.json()
        addLog(`权限更新成功: ${JSON.stringify(data)}`)
        await fetchCurrentPermission() // 重新获取权限设置
      } else {
        const errorText = await response.text()
        addLog(`权限更新失败: ${response.status} - ${errorText}`)
      }
    } catch (error) {
      addLog(`权限更新错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  // 检查权限
  const checkPermission = async () => {
    try {
      setLoading(true)
      addLog('检查当前用户权限...')
      
      const response = await fetch(`/console/api/enterprise/webapp/permission?appId=${appId}`)
      if (response.ok) {
        const data = await response.json()
        addLog(`权限检查结果: ${data.result ? '有权限' : '无权限'}`)
      } else {
        addLog(`权限检查失败: ${response.status}`)
      }
    } catch (error) {
      addLog(`权限检查错误: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCurrentPermission()
  }, [appId])

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Web应用权限测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 权限设置区域 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">权限设置</h2>
          
          <div className="space-y-2">
            <p>当前访问模式: <span className="font-mono bg-gray-100 px-2 py-1 rounded">{currentAccessMode}</span></p>
            {members.length > 0 && (
              <div>
                <p>指定成员 ({members.length}):</p>
                <ul className="list-disc list-inside ml-4">
                  {members.map(member => (
                    <li key={member.id}>{member.name} ({member.email})</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={() => updatePermission(AccessMode.PUBLIC)}
              disabled={loading}
              variant={currentAccessMode === AccessMode.PUBLIC ? 'primary' : 'secondary'}
            >
              公开访问
            </Button>
            <Button 
              onClick={() => updatePermission(AccessMode.ORGANIZATION)}
              disabled={loading}
              variant={currentAccessMode === AccessMode.ORGANIZATION ? 'primary' : 'secondary'}
            >
              组织内访问
            </Button>
            <Button 
              onClick={() => updatePermission(AccessMode.EXTERNAL_MEMBERS)}
              disabled={loading}
              variant={currentAccessMode === AccessMode.EXTERNAL_MEMBERS ? 'primary' : 'secondary'}
            >
              外部成员
            </Button>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium">指定成员访问</h3>
            <div className="flex gap-2">
              <input
                type="text"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                placeholder="搜索成员..."
                className="flex-1 px-3 py-2 border rounded"
              />
              <Button onClick={searchMembers} disabled={loading}>
                搜索
              </Button>
            </div>
            
            {searchResults.length > 0 && (
              <div className="space-y-1">
                <p>搜索结果:</p>
                {searchResults.map(subject => (
                  <div key={subject.subjectId} className="flex items-center justify-between p-2 border rounded">
                    <span>{subject.accountData?.name} ({subject.accountData?.email})</span>
                    <Button
                      size="small"
                      onClick={() => updatePermission(AccessMode.SPECIFIC_GROUPS_MEMBERS, [subject.accountData])}
                      disabled={loading}
                    >
                      设为指定成员
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* 测试区域 */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">功能测试</h2>
          
          <div className="flex flex-col gap-2">
            <Button onClick={fetchCurrentPermission} disabled={loading}>
              刷新权限设置
            </Button>
            <Button onClick={checkPermission} disabled={loading}>
              检查当前用户权限
            </Button>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">操作日志</h3>
            <div className="bg-gray-50 p-3 rounded max-h-60 overflow-y-auto">
              {logs.length === 0 ? (
                <p className="text-gray-500">暂无日志</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
            <Button 
              size="small" 
              variant="ghost" 
              onClick={() => setLogs([])}
              className="mt-2"
            >
              清空日志
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
