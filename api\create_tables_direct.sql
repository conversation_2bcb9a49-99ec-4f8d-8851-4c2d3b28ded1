-- 直接创建Web应用权限表的SQL脚本

-- 检查并创建 webapp_permissions_extend 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'webapp_permissions_extend') THEN
        CREATE TABLE webapp_permissions_extend (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            app_id UUID NOT NULL,
            tenant_id UUID NOT NULL,
            access_mode VARCHAR(32) NOT NULL DEFAULT 'public',
            created_by UUID NOT NULL,
            updated_by UUI<PERSON>,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT unique_app_webapp_permission UNIQUE (app_id)
        );
        
        -- 添加注释
        COMMENT ON TABLE webapp_permissions_extend IS 'Web应用权限设置表';
        COMMENT ON COLUMN webapp_permissions_extend.access_mode IS '访问模式: public, private, private_all, sso_verified';
        
        -- 创建索引
        CREATE INDEX idx_webapp_permissions_extend_app_id ON webapp_permissions_extend (app_id);
        CREATE INDEX idx_webapp_permissions_extend_tenant_id ON webapp_permissions_extend (tenant_id);
        CREATE INDEX idx_webapp_permissions_extend_access_mode ON webapp_permissions_extend (access_mode);
        
        RAISE NOTICE 'Table webapp_permissions_extend created successfully';
    ELSE
        RAISE NOTICE 'Table webapp_permissions_extend already exists';
    END IF;
END
$$;

-- 检查并创建 webapp_member_permissions_extend 表
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'webapp_member_permissions_extend') THEN
        CREATE TABLE webapp_member_permissions_extend (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            webapp_permission_id UUID NOT NULL,
            account_id UUID NOT NULL,
            subject_type VARCHAR(16) NOT NULL DEFAULT 'account',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT unique_webapp_member_permission UNIQUE (webapp_permission_id, account_id, subject_type)
        );
        
        -- 添加外键约束
        ALTER TABLE webapp_member_permissions_extend 
        ADD CONSTRAINT fk_webapp_member_permissions_extend_webapp_permission_id 
        FOREIGN KEY (webapp_permission_id) 
        REFERENCES webapp_permissions_extend (id) 
        ON DELETE CASCADE;
        
        -- 添加注释
        COMMENT ON TABLE webapp_member_permissions_extend IS 'Web应用成员权限关联表';
        COMMENT ON COLUMN webapp_member_permissions_extend.subject_type IS '主体类型: account, group';
        
        -- 创建索引
        CREATE INDEX idx_webapp_member_permissions_extend_webapp_id ON webapp_member_permissions_extend (webapp_permission_id);
        CREATE INDEX idx_webapp_member_permissions_extend_account_id ON webapp_member_permissions_extend (account_id);
        CREATE INDEX idx_webapp_member_permissions_extend_subject_type ON webapp_member_permissions_extend (subject_type);
        
        RAISE NOTICE 'Table webapp_member_permissions_extend created successfully';
    ELSE
        RAISE NOTICE 'Table webapp_member_permissions_extend already exists';
    END IF;
END
$$;

-- 验证表创建
SELECT 
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
AND table_name IN ('webapp_permissions_extend', 'webapp_member_permissions_extend')
ORDER BY table_name;
