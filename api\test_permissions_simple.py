#!/usr/bin/env python3
"""
简单的权限功能测试
"""
import requests
import json

# 测试配置
BASE_URL = "http://localhost:5001"
API_BASE = f"{BASE_URL}/console/api"

def test_webapp_permissions():
    """测试Web应用权限API"""
    print("开始测试Web应用权限API...")
    
    # 测试数据
    test_app_id = "test-app-123"
    
    try:
        # 1. 测试获取访问模式
        print("\n1. 测试获取访问模式...")
        response = requests.get(f"{API_BASE}/enterprise/webapp/app/access-mode", 
                              params={"appId": test_app_id})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"访问模式: {data.get('accessMode', 'unknown')}")
        else:
            print(f"错误: {response.text}")
        
        # 2. 测试设置访问模式为公开
        print("\n2. 测试设置访问模式为公开...")
        payload = {
            "appId": test_app_id,
            "accessMode": "public"
        }
        response = requests.post(f"{API_BASE}/enterprise/webapp/app/access-mode", 
                               json=payload)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"设置结果: {data}")
        else:
            print(f"错误: {response.text}")
        
        # 3. 测试设置访问模式为组织内
        print("\n3. 测试设置访问模式为组织内...")
        payload = {
            "appId": test_app_id,
            "accessMode": "private_all"
        }
        response = requests.post(f"{API_BASE}/enterprise/webapp/app/access-mode", 
                               json=payload)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"设置结果: {data}")
        else:
            print(f"错误: {response.text}")
        
        # 4. 测试权限检查
        print("\n4. 测试权限检查...")
        response = requests.get(f"{API_BASE}/enterprise/webapp/permission", 
                              params={"appId": test_app_id})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"权限检查结果: {data}")
        else:
            print(f"错误: {response.text}")
        
        # 5. 测试搜索成员
        print("\n5. 测试搜索成员...")
        response = requests.get(f"{API_BASE}/enterprise/webapp/members/search", 
                              params={"keyword": "test", "limit": 5})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"搜索结果: 找到 {len(data.get('subjects', []))} 个成员")
        else:
            print(f"错误: {response.text}")
        
        print("\n✅ API测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_database_tables():
    """测试数据库表是否存在"""
    print("测试数据库表...")
    
    try:
        # 这里需要数据库连接，暂时跳过
        print("数据库表测试需要在Flask应用上下文中运行")
        
    except Exception as e:
        print(f"数据库测试失败: {str(e)}")


if __name__ == "__main__":
    print("=" * 60)
    print("Web应用权限功能简单测试")
    print("=" * 60)
    
    # 测试API（需要服务器运行）
    try:
        test_webapp_permissions()
    except Exception as e:
        print(f"API测试跳过: {str(e)}")
    
    # 测试数据库表
    test_database_tables()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
