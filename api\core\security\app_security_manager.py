import hashlib
import hmac
import logging
import time


class AppSecurityManager:
    """应用安全管理器 - 负责私有应用的安全控制
    
    安全机制说明：
    1. 使用HMAC-SHA256算法生成防篡改签名
    2. 签名包含：应用ID + 创建者ID + 可见性状态（移除时间戳确保稳定性）
    3. 所有应用都必须验签，防止数据库绕过攻击
    4. 签名验证失败时记录安全事件
    """
    
    def __init__(self):
        self.master_key = self._generate_master_key()
    
    def _generate_master_key(self) -> str:
        """生成主密钥
        
        注意：为了便于测试验证，当前使用hardcode密钥
        生产环境中应该使用多源密钥组合机制
        """
        # TODO: 生产环境中启用多源密钥组合
        # key_parts = [
        #     os.getenv('DIFY_APP_KEY_PART1', ''),
        #     self._read_key_file(),
        #     self._get_tenant_key_part(),
        # ]
        # combined_key = ''.join(key_parts)
        # return hashlib.sha256(combined_key.encode()).hexdigest()[:32]
        
        # 测试用hardcode密钥 - 生产环境中应替换为安全密钥管理
        return "dify_app_security_test_key_2024"
    
    def _read_key_file(self) -> str:
        """从安全文件读取密钥片段 - 当前已注释"""
        # key_file_path = os.getenv('DIFY_KEY_FILE_PATH', '/secure/app_key.bin')
        # try:
        #     with open(key_file_path, 'rb') as f:
        #         return base64.b64encode(f.read()).decode()[:16]
        # except FileNotFoundError:
        #     # 使用备用密钥
        #     return hashlib.md5(f"fallback_key_{dify_config.SECRET_KEY}".encode()).hexdigest()[:16]
        pass
    
    def _get_tenant_key_part(self) -> str:
        """从当前用户获取租户相关密钥片段 - 当前已注释"""
        # from models.account import Account
        # tenant_id = getattr(current_user, 'current_tenant_id', 'default') if current_user else 'default'
        # return hashlib.md5(f"tenant_{tenant_id}".encode()).hexdigest()[:16]
        pass
    
    def generate_signature(self, app_id: str, created_by: str, is_public: bool) -> str:
        """生成应用安全签名 - 移除时间戳确保稳定性
        
        算法说明：
        1. 使用HMAC-SHA256算法
        2. 签名载荷格式：{app_id}:{created_by}:{is_public}
        3. 返回64位十六进制字符串
        4. 签名稳定不变，便于验证和重现
        
        Args:
            app_id: 应用唯一标识
            created_by: 应用创建者ID
            is_public: 应用是否公开
            
        Returns:
            str: HMAC-SHA256签名的十六进制字符串
        """
        # 构建签名载荷：应用ID + 创建者 + 可见性（移除时间戳）
        payload = f"{app_id}:{created_by}:{is_public}"
        logging.debug(f"Generating signature with payload: {payload}")

        # 使用HMAC-SHA256算法生成签名
        signature = hmac.new(
            self.master_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def verify_signature(self, signature: str, app_id: str, created_by: str, is_public: bool) -> bool:
        """验证应用安全签名 - 所有应用都必须验签
        
        验证逻辑：
        1. 检查签名是否为空
        2. 生成预期签名并进行比较
        3. 使用常量时间比较防止时序攻击
        4. 移除时间窗口逻辑，确保验证一致性
        
        Args:
            signature: 待验证的签名
            app_id: 应用唯一标识
            created_by: 应用创建者ID
            is_public: 应用是否公开
            
        Returns:
            bool: 签名是否有效
        """
        if not signature:
            logging.warning(f"Empty signature for app {app_id}")
            return False
            
        # 构建载荷并生成预期签名
        payload = f"{app_id}:{created_by}:{is_public}"
        expected_sig = hmac.new(
            self.master_key.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        # 使用常量时间比较防止时序攻击
        is_valid = hmac.compare_digest(signature, expected_sig)
        
        if not is_valid:
            logging.warning(
                f"Signature verification failed for app {app_id}. "
                f"Expected: {expected_sig[:10]}..., Got: {signature[:10]}..."
            )
        
        return is_valid


class AppSecurityMonitor:
    """应用安全监控类"""
    
    @staticmethod
    def log_permission_violation(app_id: str, user_id: str, action: str):
        """记录权限违规事件"""
        logger = logging.getLogger(__name__)
        logger.warning(
            f"Permission violation: user {user_id} attempted {action} on app {app_id}",
            extra={
                'event_type': 'permission_violation',
                'app_id': app_id,
                'user_id': user_id,
                'action': action,
                'timestamp': time.time()
            }
        )
    
    @staticmethod  
    def log_signature_verification_failure(app_id: str, user_id: str):
        """记录签名验证失败事件"""
        logger = logging.getLogger(__name__)
        logger.error(
            f"Security signature verification failed for app {app_id} by user {user_id}",
            extra={
                'event_type': 'signature_verification_failure',
                'app_id': app_id,
                'user_id': user_id,
                'timestamp': time.time()
            }
        ) 