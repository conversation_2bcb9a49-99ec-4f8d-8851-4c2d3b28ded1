"""
认证模块路由注册
"""
from flask import Blueprint

from libs.external_api import ExternalApi

# 创建认证模块的蓝图和API实例
bp = Blueprint("auth", __name__, url_prefix="/console/api/auth")
api = ExternalApi(bp)

# 导入验证码API
from .captcha_api_extend import (
    CaptchaGenerateApiExtend,
    CaptchaImageApiExtend,
    CaptchaInfoApiExtend,
    CaptchaVerifyApiExtend,
)

# 注册验证码API路由
api.add_resource(CaptchaGenerateApiExtend, "/captcha/generate")
api.add_resource(CaptchaVerifyApiExtend, "/captcha/verify")
api.add_resource(CaptchaInfoApiExtend, "/captcha/info")
api.add_resource(CaptchaImageApiExtend, "/captcha/refresh") 