#!/usr/bin/env python3
"""
直接创建Web应用权限表的脚本
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extensions.ext_database import db
from sqlalchemy import text


def create_webapp_permissions_tables():
    """直接创建Web应用权限表"""
    print("开始创建Web应用权限表...")
    
    try:
        # 检查表是否已存在
        result = db.session.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('webapp_permissions_extend', 'webapp_member_permissions_extend')
        """))
        existing_tables = [row[0] for row in result.fetchall()]
        
        # 1. 创建web应用权限设置表
        if 'webapp_permissions_extend' not in existing_tables:
            print("创建 webapp_permissions_extend 表...")
            db.session.execute(text("""
                CREATE TABLE webapp_permissions_extend (
                    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                    app_id UUID NOT NULL,
                    tenant_id UUID NOT NULL,
                    access_mode VARCHAR(32) NOT NULL DEFAULT 'public',
                    created_by UUID NOT NULL,
                    updated_by UUID,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_app_webapp_permission UNIQUE (app_id)
                );
                
                COMMENT ON TABLE webapp_permissions_extend IS 'Web应用权限设置表';
                COMMENT ON COLUMN webapp_permissions_extend.access_mode IS '访问模式: public, private, private_all, sso_verified';
                
                CREATE INDEX idx_webapp_permissions_extend_app_id ON webapp_permissions_extend (app_id);
                CREATE INDEX idx_webapp_permissions_extend_tenant_id ON webapp_permissions_extend (tenant_id);
                CREATE INDEX idx_webapp_permissions_extend_access_mode ON webapp_permissions_extend (access_mode);
            """))
            print("✅ webapp_permissions_extend 表创建成功")
        else:
            print("✅ webapp_permissions_extend 表已存在")
        
        # 2. 创建web应用成员权限关联表
        if 'webapp_member_permissions_extend' not in existing_tables:
            print("创建 webapp_member_permissions_extend 表...")
            db.session.execute(text("""
                CREATE TABLE webapp_member_permissions_extend (
                    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                    webapp_permission_id UUID NOT NULL,
                    account_id UUID NOT NULL,
                    subject_type VARCHAR(16) NOT NULL DEFAULT 'account',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT unique_webapp_member_permission UNIQUE (webapp_permission_id, account_id, subject_type),
                    CONSTRAINT fk_webapp_member_permissions_extend_webapp_permission_id 
                        FOREIGN KEY (webapp_permission_id) 
                        REFERENCES webapp_permissions_extend (id) 
                        ON DELETE CASCADE
                );
                
                COMMENT ON TABLE webapp_member_permissions_extend IS 'Web应用成员权限关联表';
                COMMENT ON COLUMN webapp_member_permissions_extend.subject_type IS '主体类型: account, group';
                
                CREATE INDEX idx_webapp_member_permissions_extend_webapp_id ON webapp_member_permissions_extend (webapp_permission_id);
                CREATE INDEX idx_webapp_member_permissions_extend_account_id ON webapp_member_permissions_extend (account_id);
                CREATE INDEX idx_webapp_member_permissions_extend_subject_type ON webapp_member_permissions_extend (subject_type);
            """))
            print("✅ webapp_member_permissions_extend 表创建成功")
        else:
            print("✅ webapp_member_permissions_extend 表已存在")
        
        # 提交事务
        db.session.commit()
        print("🎉 所有表创建完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {str(e)}")
        db.session.rollback()
        import traceback
        traceback.print_exc()
        return False


def check_tables():
    """检查表是否存在"""
    print("检查表结构...")
    
    try:
        # 检查表是否存在
        result = db.session.execute(text("""
            SELECT table_name, 
                   (SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_name = t.table_name AND table_schema = 'public') as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public' 
            AND table_name IN ('webapp_permissions_extend', 'webapp_member_permissions_extend')
            ORDER BY table_name
        """))
        
        tables = result.fetchall()
        
        if not tables:
            print("❌ 没有找到权限相关表")
            return False
        
        for table_name, column_count in tables:
            print(f"✅ 表 {table_name} 存在，包含 {column_count} 个字段")
            
            # 显示表结构
            result = db.session.execute(text(f"""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            print(f"  字段列表:")
            for col_name, data_type, is_nullable, col_default in columns:
                nullable = "NULL" if is_nullable == "YES" else "NOT NULL"
                default = f" DEFAULT {col_default}" if col_default else ""
                print(f"    {col_name}: {data_type} {nullable}{default}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查表失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("Web应用权限表创建脚本")
    print("=" * 60)
    
    # 需要在Flask应用上下文中运行
    try:
        from app_factory import create_app
        app = create_app()
        
        with app.app_context():
            # 检查现有表
            check_tables()
            
            # 创建表
            success = create_webapp_permissions_tables()
            
            if success:
                # 再次检查表
                print("\n" + "=" * 40)
                print("创建后的表结构:")
                print("=" * 40)
                check_tables()
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("=" * 60)
