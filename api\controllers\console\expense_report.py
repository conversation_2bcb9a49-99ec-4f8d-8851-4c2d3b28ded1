import csv
import io

from flask import make_response
from flask_restful import Resource, fields, marshal_with, reqparse
from sqlalchemy import text

from extensions.ext_database import db

# 1. 获取账号额度排名
account_quota_fields = {
    "account_id": fields.String,
    "ranking": fields.Integer,
    "account_name": fields.String,
    "used_quota": fields.Float,
    "total_quota": fields.Float,
}
account_quota_list_fields = {
    "list": fields.List(fields.Nested(account_quota_fields)),
    "page": fields.Integer,
    "pageSize": fields.Integer,
    "total": fields.Integer,
}

class AccountQuotaRankingApi(Resource):
    @marshal_with(account_quota_list_fields)
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, required=True, location='args')  # 新增租户ID查询条件
        parser.add_argument('page', type=int, default=1, location='args')
        parser.add_argument('pageSize', type=int, default=10, location='args')
        args = parser.parse_args()
        offset = (args['page'] - 1) * args['pageSize']

        sql = """
        SELECT
            ame.account_id,
            RANK() OVER (ORDER BY ame.used_quota DESC) AS ranking,
            a.name AS account_name,
            ame.used_quota,
            ame.total_quota
        FROM public.account_money_extend ame
        INNER JOIN accounts a ON ame.account_id = a.id
        INNER JOIN tenant_account_joins taj ON taj.account_id = a.id
        INNER JOIN tenants t ON taj.tenant_id = t.id
        WHERE t.id = :tenant_id
        ORDER BY ranking
        LIMIT :page_size OFFSET :offset
        """
        params = {
            "tenant_id": args['tenantId'],
            "page_size": args['pageSize'],
            "offset": offset
        }
        result = db.session.execute(text(sql), params)
        rows = [dict(row._mapping) for row in result]
        
        # 查询总数
        count_sql = """
        SELECT COUNT(*) FROM (
            SELECT ame.account_id
            FROM public.account_money_extend ame
            INNER JOIN accounts a ON ame.account_id = a.id
            INNER JOIN tenant_account_joins taj ON taj.account_id = a.id
            INNER JOIN tenants t ON taj.tenant_id = t.id
            WHERE t.id = :tenant_id
        ) AS subquery
        """
        count_result = db.session.execute(text(count_sql), {"tenant_id": args['tenantId']})
        total = count_result.scalar() or 0
        
        return {
            "list": rows,
            "page": args['page'],
            "pageSize": args['pageSize'],
            "total": total,
        }

# 2. 获取应用配额排名
app_quota_fields = {
    "ranking": fields.Integer,
    "app_id": fields.String,
    "total_cost": fields.Float,
    "message_cost": fields.Float,
    "workflow_cost": fields.Float,
    "record_num": fields.Integer,
    "name": fields.String,
    "mode": fields.String,
    "account_name": fields.String,
    "use_num": fields.Integer,
}
app_quota_list_fields = {
    "list": fields.List(fields.Nested(app_quota_fields)),
    "page": fields.Integer,
    "pageSize": fields.Integer,
    "total": fields.Integer,
}

class AppQuotaRankingApi(Resource):
    @marshal_with(app_quota_list_fields)
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, required=True, location='args')  # 新增租户ID查询条件
        parser.add_argument('page', type=int, default=1, location='args')
        parser.add_argument('pageSize', type=int, default=10, location='args')
        args = parser.parse_args()
        offset = (args['page'] - 1) * args['pageSize']

        sql = """
        SELECT
            RANK() OVER (ORDER BY combined.total_cost DESC) AS ranking,
            combined.app_id,
            combined.total_cost,
            combined.message_cost,
            combined.workflow_cost,
            combined.record_num,
            a.name,
            a.mode,
            acc.name AS account_name,
            stats.use_num
        FROM (
            SELECT
                COALESCE(msg.app_id, wf.app_id) AS app_id,
                COALESCE(msg.message_cost, 0) AS message_cost,
                COALESCE(wf.workflow_cost, 0) AS workflow_cost,
                COALESCE(msg.message_num, 0) + COALESCE(wf.workflow_num, 0) AS record_num,
                COALESCE(msg.message_cost, 0) + COALESCE(wf.workflow_cost, 0) AS total_cost
            FROM (
                SELECT
                    app_id,
                    COUNT(id) AS message_num,
                    SUM(total_price) AS message_cost
                FROM public.messages
                GROUP BY app_id
            ) msg
            FULL OUTER JOIN (
                SELECT
                    app_id,
                    COUNT(id) AS workflow_num,
                    SUM(CAST((execution_metadata::json->>'total_price') AS NUMERIC)) AS workflow_cost
                FROM public.workflow_node_executions
                WHERE execution_metadata IS NOT NULL
                AND execution_metadata != ''
                AND (execution_metadata::json->>'total_price') IS NOT NULL
                GROUP BY app_id
            ) wf ON msg.app_id = wf.app_id
        ) combined
        LEFT JOIN apps a ON combined.app_id = a.id
        LEFT JOIN (
            SELECT
                t.id AS tenant_id,
                ta.account_id
            FROM tenants t
            JOIN tenant_account_joins ta ON t.id = ta.tenant_id
            WHERE ta.role = 'owner'
        ) tenant_info ON a.tenant_id = tenant_info.tenant_id
        LEFT JOIN accounts acc ON tenant_info.account_id = acc.id
        LEFT JOIN (
            SELECT
                app_id,
                SUM(number) AS use_num
            FROM app_statistics_extend
            GROUP BY app_id
        ) stats ON combined.app_id = stats.app_id
        WHERE a.tenant_id = :tenant_id
        ORDER BY combined.total_cost DESC
        LIMIT :page_size OFFSET :offset
        """
        result = db.session.execute(
            text(sql),
            {
                'tenant_id': args['tenantId'],
                'page_size': args['pageSize'],
                'offset': offset
            }
        )
        rows = [dict(row._mapping) for row in result]
        
        count_sql = """
        SELECT COUNT(*) FROM (
            SELECT
                combined.app_id
            FROM (
                SELECT
                    COALESCE(msg.app_id, wf.app_id) AS app_id
                FROM (
                    SELECT app_id FROM public.messages GROUP BY app_id
                ) msg
                FULL OUTER JOIN (
                    SELECT app_id FROM public.workflow_node_executions
                    WHERE execution_metadata IS NOT NULL
                      AND execution_metadata != ''
                      AND (execution_metadata::json->>'total_price') IS NOT NULL
                    GROUP BY app_id
                ) wf ON msg.app_id = wf.app_id
            ) combined
            LEFT JOIN apps a ON combined.app_id = a.id
            WHERE a.tenant_id = :tenant_id
        ) AS subquery
        """
        count_result = db.session.execute(text(count_sql), {'tenant_id': args['tenantId']})
        total = count_result.scalar() or 0
        
        return {
            "list": rows,
            "page": args['page'],
            "pageSize": args['pageSize'],
            "total": total,
        }

# 3. 获取应用密钥配额排名
app_token_quota_fields = {
    "ranking": fields.Integer,
    "name": fields.String,
    "app_token": fields.String,
    "accumulated_quota": fields.Float,
    "day_used_quota": fields.Float,
    "month_used_quota": fields.Float,
    "day_limit_quota": fields.Float,
    "month_limit_quota": fields.Float,
}
app_token_quota_list_fields = {
    "list": fields.List(fields.Nested(app_token_quota_fields)),
    "page": fields.Integer,
    "pageSize": fields.Integer,
    "total": fields.Integer,
}

class AppTokenQuotaRankingApi(Resource):
    @marshal_with(app_token_quota_list_fields)
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, required=True, location='args')  # 新增租户ID查询条件
        parser.add_argument('page', type=int, default=1, location='args')
        parser.add_argument('pageSize', type=int, default=10, location='args')
        args = parser.parse_args()
        offset = (args['page'] - 1) * args['pageSize']

        sql = """
        SELECT
            ROW_NUMBER() OVER (ORDER BY token_money.accumulated_quota DESC) AS ranking,
            app_info.app_name AS name,
            token_info.token AS app_token,
            token_money.accumulated_quota,
            token_money.day_used_quota,
            token_money.month_used_quota,
            token_money.day_limit_quota,
            token_money.month_limit_quota
        FROM (
            SELECT
                atm.app_token_id,
                atm.accumulated_quota,
                atm.day_used_quota,
                atm.month_used_quota,
                atm.day_limit_quota,
                atm.month_limit_quota
            FROM api_token_money_extend atm
        ) token_money
        JOIN (
            SELECT
                at.id AS app_token_id,
                at.app_id,
                at.token
            FROM api_tokens at
        ) token_info ON token_money.app_token_id = token_info.app_token_id
        JOIN (
            SELECT
                a.id AS app_id,
                a.name AS app_name,
                a.tenant_id
            FROM apps a
        ) app_info ON token_info.app_id = app_info.app_id
        WHERE app_info.tenant_id = :tenant_id
        ORDER BY
            token_money.accumulated_quota DESC
        LIMIT :page_size OFFSET :offset
        """
        result = db.session.execute(
            text(sql),
            {
                'tenant_id': args['tenantId'],
                'page_size': args['pageSize'],
                'offset': offset
            }
        )
        rows = [dict(row._mapping) for row in result]
        
        # 分页总数统计
        count_sql = """
        SELECT COUNT(*) FROM (
            SELECT
                atm.app_token_id
            FROM api_token_money_extend atm
            JOIN api_tokens at ON atm.app_token_id = at.id
            JOIN apps a ON at.app_id = a.id
            WHERE a.tenant_id = :tenant_id
            GROUP BY atm.app_token_id
        ) AS subquery
        """
        count_result = db.session.execute(text(count_sql), {'tenant_id': args['tenantId']})
        total = count_result.scalar() or 0
        
        return {
            "list": rows,
            "page": args['page'],
            "pageSize": args['pageSize'],
            "total": total,
        }

# 4. 获取每天密钥花费数据
token_daily_quota_fields = {
    "stat_date": fields.String,
    "total_used": fields.Float,
}
token_daily_quota_list_fields = {
    "data": fields.List(fields.Nested(token_daily_quota_fields)),
}

class AppTokenDailyQuotaApi(Resource):
    @marshal_with(token_daily_quota_list_fields)
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, required=True, location='args')  # 新增租户ID查询条件
        args = parser.parse_args()

        sql = """
        WITH date_range AS (
            SELECT generate_series::DATE AS stat_date
            FROM generate_series(
                CURRENT_DATE - INTERVAL '15 days',
                CURRENT_DATE,
                '1 day'
            )
        ),
        msg_data AS (
            SELECT 
                to_char(m.created_at, 'yyyy-mm-dd')::DATE AS stat_date,
                SUM(m.total_price) AS total_cost
            FROM messages m
            INNER JOIN apps a ON a.id = m.app_id
            INNER JOIN tenants t ON a.tenant_id = t.id
            WHERE m.total_price != 0
              AND m.created_at >= CURRENT_DATE - INTERVAL '15 days'
              AND t.id = :tenant_id
            GROUP BY to_char(m.created_at, 'yyyy-mm-dd')
        )
        SELECT 
            dr.stat_date,
            COALESCE(SUM(md.total_cost), 0.00) AS total_used
        FROM date_range dr
        LEFT JOIN msg_data md ON dr.stat_date = md.stat_date
        GROUP BY dr.stat_date
        ORDER BY dr.stat_date
        """
        params = {"tenant_id": args['tenantId']}
        result = db.session.execute(text(sql), params)
        rows = [dict(row._mapping) for row in result]
        return {"data": rows}

# 5. 导出数据报表
class MessageExportApi(Resource):
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, default=None, location='args')
        args = parser.parse_args()

        sql = """
        SELECT
            m.app_id,
            CASE
                WHEN m.from_source = 'api' THEN t."name"
                ELSE a.email
            END AS name,
            CASE
                WHEN m.from_source = 'api' THEN t."name"
                ELSE ta_tenant."name"
            END AS tenant_name,
            a2."name" as app_name,
            m.model_provider,
            m.model_id,
            m.message_tokens,
            m.message_unit_price,
            m.answer_tokens,
            m.answer_unit_price,
            m.total_price,
            m.currency,
            m.from_source,
            m.from_end_user_id,
            m.from_account_id,
            m.created_at,
            m.workflow_run_id,
            m.status,
            m.invoke_from
        FROM
            messages m
        LEFT JOIN
            accounts a ON a.id = m.from_account_id AND m.from_source != 'api'
        LEFT JOIN
            end_users eu ON eu.id = m.from_end_user_id AND m.from_source = 'api'
        LEFT JOIN
            tenants t ON t.id = eu.tenant_id AND m.from_source = 'api'
        LEFT JOIN
            tenant_account_joins taj ON taj.account_id = m.from_account_id AND m.from_source != 'api'
        LEFT JOIN
            tenants ta_tenant ON ta_tenant.id = taj.tenant_id AND m.from_source != 'api'
        LEFT JOIN
            apps a2 ON a2.id = m.app_id
        {where_clause}
        ORDER BY m.created_at DESC
        """
        where_clause = ""
        params = {}
        if args['tenantId']:
            where_clause = """
            WHERE (
                (m.from_source = 'api' AND t."id" LIKE :tenant_id)
                OR
                (m.from_source != 'api' AND ta_tenant."id" LIKE :tenant_id)
            )
            """
            params["tenant_id"] = f"%{args['tenantId']}%"
        sql = sql.format(where_clause=where_clause)
        result = db.session.execute(text(sql), params)
        rows = [dict(row._mapping) for row in result]

        # 导出为CSV
        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=rows[0].keys() if rows else [])
        writer.writeheader()
        for row in rows:
            writer.writerow(row)
        output.seek(0)
        response = make_response(output.getvalue())
        response.headers["Content-Disposition"] = "attachment; filename=messages_export.csv"
        response.headers["Content-type"] = "text/csv"
        return response
