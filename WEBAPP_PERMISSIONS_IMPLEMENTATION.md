# Web应用访问权限功能实现

## 概述

本文档描述了Web应用访问权限功能的完整实现，包括后端API、前端组件、数据库表结构等。

## 已实现的功能

### 1. 数据库表结构

#### webapp_permissions_extend 表
- `id`: 主键UUID
- `app_id`: 应用ID
- `tenant_id`: 租户ID  
- `access_mode`: 访问模式 (public, private, private_all, sso_verified)
- `created_by`: 创建者ID
- `updated_by`: 更新者ID
- `created_at`: 创建时间
- `updated_at`: 更新时间

#### webapp_member_permissions_extend 表
- `id`: 主键UUID
- `webapp_permission_id`: 权限设置ID (外键)
- `account_id`: 账户ID
- `subject_type`: 主体类型 (account, group)
- `created_at`: 创建时间

### 2. 后端API实现

#### 权限服务 (WebAppPermissionsServiceExtend)
- `get_app_permission()`: 获取应用权限设置
- `create_or_update_app_permission()`: 创建或更新权限设置
- `check_user_access_permission()`: 检查用户访问权限
- `get_app_access_mode()`: 获取应用访问模式
- `search_members()`: 搜索租户成员
- `get_app_permission_with_members()`: 获取权限设置及成员信息

#### API端点
- `GET /console/api/enterprise/webapp/app/access-mode`: 获取访问模式
- `POST /console/api/enterprise/webapp/app/access-mode`: 更新访问模式
- `GET /console/api/enterprise/webapp/permission`: 检查用户权限
- `GET /console/api/enterprise/webapp/app/subjects`: 获取应用权限主体
- `GET /console/api/enterprise/webapp/members/search`: 搜索成员

### 3. 四种权限模式

#### public (公开访问)
- 任何人都可以访问
- 不需要权限检查

#### private_all (组织内访问)
- 只有组织内成员可以访问
- 检查用户是否在tenant_account_joins表中

#### private (指定成员访问)
- 只有指定的成员可以访问
- 检查用户是否在webapp_member_permissions_extend表中

#### sso_verified (外部SSO认证)
- 需要SSO认证的外部用户
- 当前返回false，需要根据实际SSO实现

### 4. 前端组件

#### 权限设置弹框
- 支持四种权限模式选择
- 指定成员权限支持成员搜索和多选
- 权限设置保存到后端

#### 成员搜索功能
- 支持按姓名和邮箱搜索
- 返回成员ID、姓名、邮箱、头像等信息
- 支持多选添加到权限列表

#### 权限回显
- 打开权限弹框时显示当前权限设置
- 指定成员权限会回显已选择的成员列表

### 5. 权限检查机制

#### Web应用访问检查
- 在web应用访问时检查用户权限
- 无权限时显示错误提示页面
- 提供联系管理员的选项

#### 权限错误处理
- 不同权限模式显示不同的错误信息
- 支持重试和联系管理员功能

## 已修复的问题

### 1. 事务处理问题
- **问题**: `A transaction is already begun on this Session`
- **解决**: 移除了手动开始事务的代码，使用现有事务

### 2. 数据库表不存在问题
- **问题**: 权限表不存在导致SQL错误
- **解决**: 添加了自动创建表的逻辑，确保表存在

### 3. 成员搜索功能
- **问题**: 搜索成员功能无法正常工作
- **解决**: 实现了基于SQL的成员搜索，支持按姓名和邮箱搜索

### 4. 权限数据类型匹配
- **问题**: 后端返回数据和前端期望格式不匹配
- **解决**: 统一了数据格式，确保前后端数据类型一致

### 5. 权限保存功能
- **问题**: 四种权限模式无法正确保存
- **解决**: 实现了完整的权限保存逻辑，支持成员权限关联

### 6. 权限回显功能
- **问题**: 修改权限后再次打开弹框无法显示正确权限
- **解决**: 实现了权限回显，包括指定成员的回显

## 文件结构

```
api/
├── controllers/console/enterprise/webapp_extend.py  # API控制器
├── services/webapp_permissions_service_extend.py   # 权限服务
├── models/webapp_permissions_extend.py             # 数据模型
├── migrations_extend/versions/                     # 数据库迁移
├── create_tables_direct.sql                       # 直接创建表的SQL
├── create_webapp_permissions_tables.py            # 表创建脚本
└── test_webapp_permissions.py                     # 测试脚本

web/
├── app/components/app/app-access-control/          # 权限控制组件
├── app/components/base/webapp-permission-error.tsx # 权限错误组件
├── app/components/app/webapp-permission-test.tsx   # 权限测试组件
├── service/access-control.ts                      # 权限服务
├── utils/webapp-permission.ts                     # 权限工具函数
└── i18n/                                          # 国际化文件
```

## 使用方法

### 1. 创建数据库表
```bash
# 方法1: 使用SQL脚本
psql -d your_database -f api/create_tables_direct.sql

# 方法2: 使用Python脚本
cd api && python create_webapp_permissions_tables.py
```

### 2. 设置权限
```javascript
// 前端调用
const response = await fetch('/console/api/enterprise/webapp/app/access-mode', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    appId: 'your-app-id',
    accessMode: 'private',
    subjects: [
      { subjectId: 'user-id-1', subjectType: 'account' },
      { subjectId: 'user-id-2', subjectType: 'account' }
    ]
  })
})
```

### 3. 检查权限
```javascript
// 前端检查权限
const response = await fetch(`/console/api/enterprise/webapp/permission?appId=${appId}`)
const data = await response.json()
const hasPermission = data.result
```

## 测试

### 1. 使用测试组件
```jsx
import WebAppPermissionTest from '@/components/app/webapp-permission-test'

<WebAppPermissionTest appId="your-app-id" />
```

### 2. 使用测试脚本
```bash
cd api && python test_webapp_permissions.py
```

## 注意事项

1. **权限检查**: 确保在web应用访问时进行权限检查
2. **错误处理**: 权限不足时显示友好的错误页面
3. **数据一致性**: 删除应用时需要清理相关权限数据
4. **性能优化**: 大量成员时考虑分页和缓存
5. **安全性**: 确保权限检查不能被绕过

## 后续优化

1. 支持用户组权限
2. 权限继承机制
3. 权限审计日志
4. 批量权限操作
5. 权限模板功能
