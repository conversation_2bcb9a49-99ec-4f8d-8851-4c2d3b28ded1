from flask_restful import Resource, fields, marshal_with, reqparse
from sqlalchemy import text

from extensions.ext_database import db

# 字段定义，根据实际表结构补充
tenant_money_extend_fields = {
    "id": fields.String,
    "tenant_id": fields.String,
    "total_quota": fields.Float,
    "used_quota": fields.Float,
    "remain_quota": fields.Float,
    "created_at": fields.DateTime,
    "updated_at": fields.DateTime,
    # 如有其它字段请补充
}

class TenantMoneyExtendApi(Resource):
    @marshal_with(tenant_money_extend_fields)
    def get(self):
        parser = reqparse.RequestParser()
        parser.add_argument('tenantId', type=str, required=True, location='args')
        args = parser.parse_args()
         # 如果没有提供tenantId参数，使用当前用户的租户ID
        if not args.get('tenantId'):
            from flask_login import current_user
            args['tenantId'] = current_user.current_tenant_id   

        sql = """
        SELECT id, tenant_id, total_quota, used_quota, created_at, updated_at
        FROM tenant_money_extend
        WHERE tenant_id = :tenant_id
        """
        result = db.session.execute(text(sql), {"tenant_id": args['tenantId']})
        row = result.fetchone()
        if row:
            data = dict(row._mapping)
            # 增加剩余额度字段
            data["remain_quota"] = float(data["total_quota"] or 0) - float(data["used_quota"] or 0)
            return data
        else:
            return {}, 404