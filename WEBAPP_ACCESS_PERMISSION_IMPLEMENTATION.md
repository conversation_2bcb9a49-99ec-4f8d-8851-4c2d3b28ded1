# Web应用访问权限检查实现

## 概述

本文档描述了在用户打开应用进入详情时进行权限检查的完整实现，包括四种权限模式的具体检查逻辑。

## 权限检查逻辑

### 四种权限模式

1. **任何人 (public)**
   - 不做任何检查，直接允许访问
   - 适用于完全公开的应用

2. **平台内所有成员 (private_all)**
   - 检查用户是否是平台成员
   - 查询 `tenant_account_joins` 表验证用户是否属于该租户

3. **指定成员 (private)**
   - 检查用户是否在指定成员列表中
   - 查询 `webapp_member_permissions_extend` 表验证用户权限

4. **经认证的外部用户 (sso_verified)**
   - 不做检查，直接允许访问
   - 假设外部用户已通过SSO认证

## 实现细节

### 后端权限检查服务

#### WebAppPermissionsServiceExtend.check_user_access_permission()

```python
@staticmethod
def check_user_access_permission(app_id: str, tenant_id: str, user_id: str) -> bool:
    """
    检查用户是否有访问权限
    
    权限检查逻辑：
    1. 任何人(public) - 直接允许访问
    2. 平台内所有成员(private_all) - 检查用户是否是平台成员
    3. 指定成员(private) - 检查用户是否在webapp_member_permissions_extend表中
    4. 经认证的外部用户(sso_verified) - 不做检查，直接允许访问
    """
```

#### 权限检查流程

1. 获取应用权限设置
2. 根据访问模式执行相应检查：
   - `public`: 返回 True
   - `sso_verified`: 返回 True
   - `private_all`: 查询 `tenant_account_joins` 表
   - `private`: 查询 `webapp_member_permissions_extend` 表

### 后端API修改

#### AppWebAuthPermission 类 (api/controllers/web/app.py)

- 修改了 `/webapp/permission` 端点
- 优先使用新的权限服务进行检查
- 保留原有逻辑作为备用方案

```python
# 使用新的权限服务检查权限
has_permission = WebAppPermissionsServiceExtend.check_user_access_permission(
    app_id=app_id,
    tenant_id=tenant_id,
    user_id=user_id
)
```

### 前端权限检查

#### 应用详情页面权限检查

在 `web/app/(commonLayout)/app/(appDetailLayout)/[appId]/layout-main.tsx` 中：

1. 使用 `useGetUserCanAccessApp` Hook 检查权限
2. 在权限检查失败时显示错误页面
3. 提供联系管理员和重试功能

```tsx
// 权限检查
const { isPending: isCheckingAppAccess, data: userCanAccessResult } = useGetUserCanAccessApp({
  appId,
  isInstalledApp: true,
  enabled: true,
})

// 权限检查失败时显示错误页面
if (userCanAccessResult && !userCanAccessResult.result) {
  return (
    <WebAppPermissionError 
      accessMode={appDetail?.access_mode || 'private'}
      onContactAdmin={() => console.log('Contact admin')}
      onRetry={() => window.location.reload()}
    />
  )
}
```

#### Web应用访问权限检查

在 `web/app/components/base/chat/embedded-chatbot/hooks.tsx` 中：

- 已有的权限检查逻辑继续使用
- 通过 `useGetUserCanAccessApp` Hook 进行权限验证

## 数据库表结构

### webapp_permissions_extend 表

存储应用权限设置：

```sql
CREATE TABLE webapp_permissions_extend (
    id UUID PRIMARY KEY,
    app_id UUID NOT NULL,
    tenant_id UUID NOT NULL,
    access_mode VARCHAR(32) NOT NULL DEFAULT 'public',
    created_by UUID NOT NULL,
    updated_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### webapp_member_permissions_extend 表

存储指定成员权限：

```sql
CREATE TABLE webapp_member_permissions_extend (
    id UUID PRIMARY KEY,
    webapp_permission_id UUID NOT NULL,
    account_id UUID NOT NULL,
    subject_type VARCHAR(16) NOT NULL DEFAULT 'account',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 权限检查时机

### 1. 应用详情页面访问

- **位置**: `layout-main.tsx`
- **时机**: 用户访问 `/app/[appId]/*` 路由时
- **检查**: 调用 `useGetUserCanAccessApp` Hook

### 2. Web应用直接访问

- **位置**: `embedded-chatbot/hooks.tsx`
- **时机**: 用户访问 `/chatbot/[token]` 等Web应用时
- **检查**: 调用 `useGetUserCanAccessApp` Hook

### 3. API访问权限检查

- **位置**: `api/controllers/web/app.py`
- **时机**: 前端调用 `/webapp/permission` API时
- **检查**: 调用 `WebAppPermissionsServiceExtend.check_user_access_permission`

## 错误处理

### 权限不足时的处理

1. **应用详情页面**: 显示 `WebAppPermissionError` 组件
2. **Web应用访问**: 显示权限错误页面
3. **API调用**: 返回 `{"result": false}`

### 错误页面功能

- 显示权限不足的原因
- 提供联系管理员选项
- 提供重试功能
- 支持多语言显示

## 测试

### 后端测试

使用 `api/test_webapp_access_permissions.py` 测试：

```bash
cd api && python test_webapp_access_permissions.py
```

### 前端测试

使用 `WebAppPermissionTest` 组件测试：

```tsx
<WebAppPermissionTest appId="your-app-id" />
```

## 配置

### 启用权限检查

权限检查功能通过系统特性开关控制：

```python
features = FeatureService.get_system_features()
if features.webapp_auth.enabled:
    # 执行权限检查
```

### 权限模式配置

通过管理后台设置应用权限：

1. 进入应用设置
2. 选择访问控制
3. 设置权限模式和指定成员

## 安全考虑

1. **默认拒绝**: 出现异常时默认拒绝访问
2. **日志记录**: 记录权限检查结果用于审计
3. **备用方案**: 新权限服务失败时使用原有逻辑
4. **输入验证**: 验证所有输入参数

## 性能优化

1. **缓存**: 可考虑缓存权限检查结果
2. **索引**: 数据库表已添加必要索引
3. **批量查询**: 避免N+1查询问题
4. **异步检查**: 前端使用React Query进行异步权限检查

## 监控和日志

- 权限检查结果会记录到日志
- 可通过日志分析权限使用情况
- 支持权限审计和合规检查
