
import logging

from core.model_runtime.entities.model_entities import ModelUsage
from services import pricing_rules_service_extend


class UsageMarkupAspectExtend:
    """
    Aspect for applying markup to model usage
    """
    
    @staticmethod
    def apply_markup(usage: ModelUsage, tenant_id: str, provider: str, model: str) -> ModelUsage:
        """
        Apply markup to usage information
        
        Args:
            usage: The original usage information
            tenant_id: The tenant ID
            provider: The model provider
            model: The model name
            
        Returns:
            The usage information with markup applied
        """
        logging.info(f" apply_markup {tenant_id} provider:{provider} model_name:{model} ,usage {usage}")
        # Get pricing rule
        pricing_rule = pricing_rules_service_extend.get_pricing_rule(
            tenant_id=tenant_id,
            provider=provider,
            model_name=model,
            usage_mode=None
        )
        
        # If no pricing rule, keep original usage without markup
        if not pricing_rule:
            logging.debug(f" apply_markup no pricing rule {tenant_id} provider:{provider} model:{model} usage {usage}")
            return usage
            
        # Only apply markup to total_price and record original price and markup rate
        if hasattr(usage, 'total_price') and usage.total_price is not None:
            # Record original total price for audit
            usage.original_total_price = usage.total_price
            
            # Record markup rate for traceability  
            usage.markup_rate = pricing_rule.markup_rate
            
            # Apply markup only to total_price
            usage.total_price = usage.total_price * pricing_rule.markup_rate
            
        return usage 