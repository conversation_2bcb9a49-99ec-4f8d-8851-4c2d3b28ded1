// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/anaconda
{
	"name": "Python 3.12",
	"build": {
		"context": "..",
		"dockerfile": "Dockerfile"
	},
	"features": {
		"ghcr.io/devcontainers/features/node:1": {
			"nodeGypDependencies": true,
			"version": "lts"
		},
		"ghcr.io/devcontainers-contrib/features/npm-package:1": {
			"package": "typescript",
			"version": "latest"
		},
		"ghcr.io/devcontainers/features/docker-in-docker:2": {
			"moby": true,
			"azureDnsAutoDetection": true,
			"installDockerBuildx": true,
			"version": "latest",
			"dockerDashComposeVersion": "v2"
		}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.pylint",
				"GitHub.copilot",
				"ms-python.python"
			]
		}
	},
	"postStartCommand": "./.devcontainer/post_start_command.sh",
	"postCreateCommand": "./.devcontainer/post_create_command.sh"

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "python --version",

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
