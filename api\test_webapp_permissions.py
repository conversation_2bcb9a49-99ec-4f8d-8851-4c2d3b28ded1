#!/usr/bin/env python3
"""
Web应用权限功能测试脚本
"""
import os
import sys
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from extensions.ext_database import db
from models.webapp_permissions_extend import (
    WebAppAccessMode,
    WebAppMemberPermissionExtend,
    WebAppPermissionExtend,
    WebAppSubjectType,
)
from services.webapp_permissions_service_extend import WebAppPermissionsServiceExtend


def test_webapp_permissions():
    """测试Web应用权限功能"""
    print("开始测试Web应用权限功能...")
    
    # 测试数据
    app_id = str(uuid.uuid4())
    tenant_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    member_ids = [str(uuid.uuid4()), str(uuid.uuid4())]
    
    try:
        # 1. 测试创建公开权限
        print("\n1. 测试创建公开权限...")
        permission = WebAppPermissionsServiceExtend.create_or_update_app_permission(
            app_id=app_id,
            tenant_id=tenant_id,
            access_mode=WebAppAccessMode.PUBLIC,
            created_by=user_id
        )
        print(f"✅ 创建公开权限成功: {permission.access_mode}")
        
        # 2. 测试获取权限设置
        print("\n2. 测试获取权限设置...")
        retrieved_permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
        assert retrieved_permission is not None
        assert retrieved_permission.access_mode == WebAppAccessMode.PUBLIC
        print(f"✅ 获取权限设置成功: {retrieved_permission.access_mode}")
        
        # 3. 测试更新为指定成员权限
        print("\n3. 测试更新为指定成员权限...")
        permission = WebAppPermissionsServiceExtend.create_or_update_app_permission(
            app_id=app_id,
            tenant_id=tenant_id,
            access_mode=WebAppAccessMode.PRIVATE,
            member_ids=member_ids,
            created_by=user_id
        )
        print(f"✅ 更新为指定成员权限成功: {permission.access_mode}")
        print(f"   成员数量: {len(permission.member_permissions)}")
        
        # 4. 测试权限检查
        print("\n4. 测试权限检查...")
        
        # 检查指定成员权限
        has_permission = WebAppPermissionsServiceExtend.check_user_access_permission(
            app_id=app_id,
            tenant_id=tenant_id,
            user_id=member_ids[0]
        )
        print(f"✅ 指定成员权限检查: {has_permission}")
        
        # 检查非指定成员权限
        has_permission = WebAppPermissionsServiceExtend.check_user_access_permission(
            app_id=app_id,
            tenant_id=tenant_id,
            user_id=str(uuid.uuid4())
        )
        print(f"✅ 非指定成员权限检查: {has_permission}")
        
        # 5. 测试获取权限详情
        print("\n5. 测试获取权限详情...")
        permission_data = WebAppPermissionsServiceExtend.get_app_permission_with_members(
            app_id=app_id,
            tenant_id=tenant_id
        )
        print(f"✅ 获取权限详情成功: {permission_data['accessMode']}")
        print(f"   成员数量: {len(permission_data['members'])}")
        
        # 6. 测试搜索成员
        print("\n6. 测试搜索成员...")
        members = WebAppPermissionsServiceExtend.search_members(
            tenant_id=tenant_id,
            keyword="test",
            limit=10
        )
        print(f"✅ 搜索成员成功: 找到 {len(members)} 个成员")
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


def test_permission_models():
    """测试权限模型"""
    print("\n开始测试权限模型...")
    
    try:
        # 创建权限设置
        permission = WebAppPermissionExtend(
            app_id=str(uuid.uuid4()),
            tenant_id=str(uuid.uuid4()),
            access_mode=WebAppAccessMode.PRIVATE,
            created_by=str(uuid.uuid4())
        )
        
        # 测试枚举属性
        assert permission.access_mode_enum == WebAppAccessMode.PRIVATE
        print("✅ 权限模型枚举属性测试通过")
        
        # 测试转换为字典
        permission_dict = permission.to_dict()
        assert 'access_mode' in permission_dict
        assert permission_dict['access_mode'] == WebAppAccessMode.PRIVATE
        print("✅ 权限模型字典转换测试通过")
        
        # 创建成员权限
        member_permission = WebAppMemberPermissionExtend(
            webapp_permission_id=permission.id,
            account_id=str(uuid.uuid4()),
            subject_type=WebAppSubjectType.ACCOUNT
        )
        
        # 测试枚举属性
        assert member_permission.subject_type_enum == WebAppSubjectType.ACCOUNT
        print("✅ 成员权限模型枚举属性测试通过")
        
        # 测试转换为字典
        member_dict = member_permission.to_dict()
        assert 'subject_type' in member_dict
        assert member_dict['subject_type'] == WebAppSubjectType.ACCOUNT
        print("✅ 成员权限模型字典转换测试通过")
        
        print("🎉 权限模型测试通过！")
        
    except Exception as e:
        print(f"❌ 权限模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    print("=" * 60)
    print("Web应用权限功能测试")
    print("=" * 60)
    
    # 测试权限模型
    model_test_passed = test_permission_models()
    
    # 如果有数据库连接，测试服务功能
    try:
        # 这里需要初始化Flask应用上下文才能使用数据库
        print("\n注意: 需要在Flask应用上下文中运行服务测试")
        print("可以通过以下方式运行完整测试:")
        print("python -c \"from app_factory import create_app; app = create_app(); app.app_context().push(); exec(open('test_webapp_permissions.py').read())\"")
    except Exception as e:
        print(f"数据库测试跳过: {str(e)}")
    
    print("\n" + "=" * 60)
    if model_test_passed:
        print("✅ 基础测试通过")
    else:
        print("❌ 基础测试失败")
    print("=" * 60)
