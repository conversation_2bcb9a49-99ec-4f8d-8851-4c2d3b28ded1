import os
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class LicenseConfig(BaseSettings):
    """License管理系统配置 - 简化版"""
    
    # RSA公钥配置 - 用于验证JWT Token
    LICENSE_PUBLIC_KEY: str = Field(
        description="RSA public key for JWT verification",
        default="",
    )
    
    # 远程API配置 - 用于获取最新License
    APS_ADMIN_API_URL: str = Field(
        description="APS admin API URL for license operations",
        default="",
    )
    
    # 通知配置
    LICENSE_NOTIFICATION_ENABLED: bool = Field(
        description="Enable license notification",
        default=True,
    )
    
    # 缓存配置
    LICENSE_CACHE_TTL: int = Field(
        description="License cache TTL in seconds",
        default=300,  # 5分钟
    )
    

    
    # 自动更新配置
    LICENSE_AUTO_UPDATE_ENABLED: bool = Field(
        description="Enable automatic license update",
        default=True,
    )
    
    # 安全配置
    LICENSE_REQUIRE_HTTPS: bool = Field(
        description="Require HTTPS for license operations",
        default=True,
    )
    
    # 环境变量映射
    class Config:
        env_prefix = ""
        case_sensitive = True
        
        # 从环境变量读取配置
        @classmethod
        def customise_sources(cls, init_settings, env_settings, file_secret_settings):
            return (
                init_settings,
                env_settings,
                file_secret_settings,
            )
    
    @property
    def is_license_enabled(self) -> bool:
        """检查License功能是否启用"""
        return bool(self.LICENSE_PUBLIC_KEY and self.APS_ADMIN_API_URL)
    
    @property
    def public_key_formatted(self) -> Optional[str]:
        """格式化公钥，确保包含正确的头尾"""
        if not self.LICENSE_PUBLIC_KEY:
            return None
            
        key = self.LICENSE_PUBLIC_KEY.strip()
        
        # 如果已经包含头尾，直接返回
        if key.startswith('-----BEGIN PUBLIC KEY-----'):
            return key
            
        # 否则添加头尾
        if not key.startswith('-----'):
            key = f"-----BEGIN PUBLIC KEY-----\n{key}\n-----END PUBLIC KEY-----"
            
        return key
    
    def get_cache_key(self, cache_type: str, identifier: str) -> str:
        """生成缓存键"""
        return f"license:{cache_type}:{identifier}"
    
    def get_notification_url(self, tenant_id: str) -> Optional[str]:
        """生成通知URL"""
        if not self.APS_ADMIN_API_URL:
            return None
        return f"{self.APS_ADMIN_API_URL.rstrip('/')}/admin-api/app/license/notify?tenantsId={tenant_id}"

    
    def get_license_fetch_url(self, tenant_id: str) -> Optional[str]:
        """生成License获取URL"""
        if not self.APS_ADMIN_API_URL:
            return None
        return f"{self.APS_ADMIN_API_URL.rstrip('/')}/admin-api/app/license/list?tenantsId={tenant_id}"


# 全局配置实例
license_config = LicenseConfig()


def get_license_config() -> LicenseConfig:
    """获取License配置实例"""
    return license_config


# 配置验证函数
def validate_license_config() -> tuple[bool, list[str]]:
    """验证License配置的完整性"""
    errors = []
    
    if not license_config.LICENSE_PUBLIC_KEY:
        errors.append("LICENSE_PUBLIC_KEY is required")
    
    if not license_config.APS_ADMIN_API_URL:
        errors.append("APS_ADMIN_API_URL is required")
    
    if license_config.LICENSE_CACHE_TTL <= 0:
        errors.append("LICENSE_CACHE_TTL must be positive")
    
    # 验证公钥格式
    try:
        formatted_key = license_config.public_key_formatted
        if formatted_key and not (
            "-----BEGIN PUBLIC KEY-----" in formatted_key and 
            "-----END PUBLIC KEY-----" in formatted_key
        ):
            errors.append("LICENSE_PUBLIC_KEY format is invalid")
    except Exception as e:
        errors.append(f"LICENSE_PUBLIC_KEY validation error: {str(e)}")
    
    return len(errors) == 0, errors


# 配置初始化检查
def init_license_config():
    """初始化License配置并进行验证"""
    is_valid, errors = validate_license_config()
    
    if not is_valid:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"License configuration validation failed: {', '.join(errors)}")
        
        # 在开发环境下，可以选择不启用License功能
        if os.getenv("FLASK_ENV") == "development":
            logger.info("License functionality will be disabled in development mode")
        else:
            # 在生产环境下，配置错误应该被重视
            logger.error("License configuration errors detected in production environment")
    
    return is_valid, errors 