"""Web应用权限服务扩展"""
import logging
from typing import Dict, List, Optional, Tuple

from flask_login import current_user
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload

from extensions.ext_database import db
from models.account import Account, TenantAccountJoin
from models.model import App
from models.webapp_permissions_extend import (
    WebAppAccessMode,
    WebAppMemberPermissionExtend,
    WebAppPermissionExtend,
    WebAppSubjectType,
)

logger = logging.getLogger(__name__)


class WebAppPermissionsServiceExtend:
    """Web应用权限服务扩展"""

    @staticmethod
    def get_app_permission(app_id: str, tenant_id: str) -> Optional[WebAppPermissionExtend]:
        """获取应用权限设置"""
        try:
            return db.session.query(WebAppPermissionExtend).options(
                joinedload(WebAppPermissionExtend.member_permissions)
            ).filter(
                WebAppPermissionExtend.app_id == app_id,
                WebAppPermissionExtend.tenant_id == tenant_id
            ).first()
        except Exception as e:
            logger.error(f"Failed to get app permission for app_id={app_id}: {str(e)}")
            return None

    @staticmethod
    def create_or_update_app_permission(
        app_id: str,
        tenant_id: str,
        access_mode: str,
        member_ids: Optional[List[str]] = None,
        created_by: Optional[str] = None
    ) -> WebAppPermissionExtend:
        """创建或更新应用权限设置"""
        try:
            # 验证访问模式
            if access_mode not in [mode.value for mode in WebAppAccessMode]:
                raise ValueError(f"Invalid access mode: {access_mode}")

            # 获取或创建权限设置
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            
            if permission:
                # 更新现有权限
                permission.access_mode = access_mode
                permission.updated_by = created_by or current_user.id
                
                # 清除现有成员权限
                db.session.query(WebAppMemberPermissionExtend).filter(
                    WebAppMemberPermissionExtend.webapp_permission_id == permission.id
                ).delete()
            else:
                # 创建新权限
                permission = WebAppPermissionExtend(
                    app_id=app_id,
                    tenant_id=tenant_id,
                    access_mode=access_mode,
                    created_by=created_by or current_user.id
                )
                db.session.add(permission)
                db.session.flush()  # 获取ID

            # 如果是指定成员模式，添加成员权限
            if access_mode == WebAppAccessMode.PRIVATE and member_ids:
                for member_id in member_ids:
                    member_permission = WebAppMemberPermissionExtend(
                        webapp_permission_id=permission.id,
                        account_id=member_id,
                        subject_type=WebAppSubjectType.ACCOUNT
                    )
                    db.session.add(member_permission)

            db.session.commit()
            
            # 重新查询以获取完整数据
            return WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to create/update app permission: {str(e)}")
            raise

    @staticmethod
    def check_user_access_permission(app_id: str, tenant_id: str, user_id: str) -> bool:
        """检查用户是否有访问权限"""
        try:
            # 获取应用权限设置
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            
            if not permission:
                # 没有权限设置，默认允许访问
                return True
            
            access_mode = permission.access_mode
            
            # 公开访问
            if access_mode == WebAppAccessMode.PUBLIC:
                return True
            
            # 组织内所有成员访问
            if access_mode == WebAppAccessMode.PRIVATE_ALL:
                # 检查用户是否是该租户的成员
                tenant_member = db.session.query(TenantAccountJoin).filter(
                    TenantAccountJoin.tenant_id == tenant_id,
                    TenantAccountJoin.account_id == user_id
                ).first()
                return tenant_member is not None
            
            # 指定成员访问
            if access_mode == WebAppAccessMode.PRIVATE:
                # 检查用户是否在指定成员列表中
                member_permission = db.session.query(WebAppMemberPermissionExtend).filter(
                    WebAppMemberPermissionExtend.webapp_permission_id == permission.id,
                    WebAppMemberPermissionExtend.account_id == user_id,
                    WebAppMemberPermissionExtend.subject_type == WebAppSubjectType.ACCOUNT
                ).first()
                return member_permission is not None
            
            # SSO验证用户访问（暂时返回False，需要根据实际SSO实现）
            if access_mode == WebAppAccessMode.SSO_VERIFIED:
                return False
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to check user access permission: {str(e)}")
            return False

    @staticmethod
    def get_app_access_mode(app_id: str, tenant_id: str) -> str:
        """获取应用访问模式"""
        try:
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            return permission.access_mode if permission else WebAppAccessMode.PUBLIC
        except Exception as e:
            logger.error(f"Failed to get app access mode: {str(e)}")
            return WebAppAccessMode.PUBLIC

    @staticmethod
    def search_members(tenant_id: str, keyword: str = "", limit: int = 20) -> List[Dict]:
        """搜索租户成员"""
        try:
            query = db.session.query(Account, TenantAccountJoin).join(
                TenantAccountJoin, Account.id == TenantAccountJoin.account_id
            ).filter(
                TenantAccountJoin.tenant_id == tenant_id
            )
            
            if keyword:
                query = query.filter(
                    or_(
                        Account.name.ilike(f"%{keyword}%"),
                        Account.email.ilike(f"%{keyword}%")
                    )
                )
            
            results = query.limit(limit).all()
            
            members = []
            for account, tenant_join in results:
                members.append({
                    "id": account.id,
                    "name": account.name,
                    "email": account.email,
                    "avatar": account.avatar,
                    "avatarUrl": account.avatar,
                    "role": tenant_join.role
                })
            
            return members
            
        except Exception as e:
            logger.error(f"Failed to search members: {str(e)}")
            return []

    @staticmethod
    def get_app_permission_with_members(app_id: str, tenant_id: str) -> Dict:
        """获取应用权限设置及成员信息"""
        try:
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            
            if not permission:
                return {
                    "accessMode": WebAppAccessMode.PUBLIC,
                    "members": []
                }
            
            # 获取指定成员信息
            members = []
            if permission.access_mode == WebAppAccessMode.PRIVATE and permission.member_permissions:
                member_ids = [mp.account_id for mp in permission.member_permissions 
                             if mp.subject_type == WebAppSubjectType.ACCOUNT]
                
                if member_ids:
                    accounts = db.session.query(Account).filter(
                        Account.id.in_(member_ids)
                    ).all()
                    
                    for account in accounts:
                        members.append({
                            "id": account.id,
                            "name": account.name,
                            "email": account.email,
                            "avatar": account.avatar,
                            "avatarUrl": account.avatar
                        })
            
            return {
                "accessMode": permission.access_mode,
                "members": members
            }
            
        except Exception as e:
            logger.error(f"Failed to get app permission with members: {str(e)}")
            return {
                "accessMode": WebAppAccessMode.PUBLIC,
                "members": []
            }
