"""Web应用权限服务扩展"""
import logging
from typing import Dict, List, Optional, Tuple

from flask_login import current_user
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload

from extensions.ext_database import db
from models.account import Account, TenantAccountJoin
from models.model import App

logger = logging.getLogger(__name__)


# 权限模式枚举
class WebAppAccessMode:
    PUBLIC = "public"
    PRIVATE = "private"
    PRIVATE_ALL = "private_all"
    SSO_VERIFIED = "sso_verified"


class WebAppSubjectType:
    ACCOUNT = "account"
    GROUP = "group"


class WebAppPermissionsServiceExtend:
    """Web应用权限服务扩展"""

    @staticmethod
    def _ensure_tables_exist():
        """确保权限表存在"""
        try:
            from sqlalchemy import text
            # 检查表是否存在
            result = db.session.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name IN ('webapp_permissions_extend', 'webapp_member_permissions_extend')
            """))
            existing_tables = [row[0] for row in result.fetchall()]

            # 如果表不存在，创建表
            if 'webapp_permissions_extend' not in existing_tables:
                logger.info("Creating webapp_permissions_extend table...")
                db.session.execute(text("""
                    CREATE TABLE webapp_permissions_extend (
                        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                        app_id UUID NOT NULL,
                        tenant_id UUID NOT NULL,
                        access_mode VARCHAR(32) NOT NULL DEFAULT 'public',
                        created_by UUID NOT NULL,
                        updated_by UUID,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        CONSTRAINT unique_app_webapp_permission UNIQUE (app_id)
                    );

                    CREATE INDEX idx_webapp_permissions_extend_app_id ON webapp_permissions_extend (app_id);
                    CREATE INDEX idx_webapp_permissions_extend_tenant_id ON webapp_permissions_extend (tenant_id);
                    CREATE INDEX idx_webapp_permissions_extend_access_mode ON webapp_permissions_extend (access_mode);
                """))

            if 'webapp_member_permissions_extend' not in existing_tables:
                logger.info("Creating webapp_member_permissions_extend table...")
                db.session.execute(text("""
                    CREATE TABLE webapp_member_permissions_extend (
                        id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
                        webapp_permission_id UUID NOT NULL,
                        account_id UUID NOT NULL,
                        subject_type VARCHAR(16) NOT NULL DEFAULT 'account',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        CONSTRAINT unique_webapp_member_permission UNIQUE (webapp_permission_id, account_id, subject_type)
                    );

                    CREATE INDEX idx_webapp_member_permissions_extend_webapp_id ON webapp_member_permissions_extend (webapp_permission_id);
                    CREATE INDEX idx_webapp_member_permissions_extend_account_id ON webapp_member_permissions_extend (account_id);
                    CREATE INDEX idx_webapp_member_permissions_extend_subject_type ON webapp_member_permissions_extend (subject_type);
                """))

                # 添加外键约束（如果webapp_permissions_extend表存在）
                if 'webapp_permissions_extend' in existing_tables or 'webapp_permissions_extend' not in existing_tables:
                    try:
                        db.session.execute(text("""
                            ALTER TABLE webapp_member_permissions_extend
                            ADD CONSTRAINT fk_webapp_member_permissions_extend_webapp_permission_id
                            FOREIGN KEY (webapp_permission_id)
                            REFERENCES webapp_permissions_extend (id)
                            ON DELETE CASCADE;
                        """))
                    except Exception as e:
                        logger.warning(f"Failed to add foreign key constraint: {str(e)}")

            db.session.commit()
            logger.info("Permission tables ensured to exist")

        except Exception as e:
            logger.error(f"Failed to ensure tables exist: {str(e)}")
            db.session.rollback()

    @staticmethod
    def get_app_permission(app_id: str, tenant_id: str) -> Optional[Dict]:
        """获取应用权限设置"""
        try:
            from sqlalchemy import text
            result = db.session.execute(text("""
                SELECT id, app_id, tenant_id, access_mode, created_by, updated_by, created_at, updated_at
                FROM webapp_permissions_extend
                WHERE app_id = :app_id AND tenant_id = :tenant_id
            """), {"app_id": app_id, "tenant_id": tenant_id})

            row = result.fetchone()
            if row:
                return {
                    "id": str(row[0]),
                    "app_id": str(row[1]),
                    "tenant_id": str(row[2]),
                    "access_mode": row[3],
                    "created_by": str(row[4]),
                    "updated_by": str(row[5]) if row[5] else None,
                    "created_at": row[6],
                    "updated_at": row[7]
                }
            return None
        except Exception as e:
            logger.error(f"Failed to get app permission for app_id={app_id}: {str(e)}")
            return None

    @staticmethod
    def create_or_update_app_permission(
        app_id: str,
        tenant_id: str,
        access_mode: str,
        member_ids: Optional[List[str]] = None,
        created_by: Optional[str] = None
    ) -> Dict:
        """创建或更新应用权限设置"""
        try:
            # 确保表存在
            WebAppPermissionsServiceExtend._ensure_tables_exist()

            # 验证访问模式
            valid_modes = [WebAppAccessMode.PUBLIC, WebAppAccessMode.PRIVATE,
                          WebAppAccessMode.PRIVATE_ALL, WebAppAccessMode.SSO_VERIFIED]
            if access_mode not in valid_modes:
                raise ValueError(f"Invalid access mode: {access_mode}")

            from sqlalchemy import text
            import uuid

            # 获取现有权限设置
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)

            if permission:
                # 更新现有权限
                db.session.execute(text("""
                    UPDATE webapp_permissions_extend
                    SET access_mode = :access_mode,
                        updated_by = :updated_by,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE app_id = :app_id AND tenant_id = :tenant_id
                """), {
                    "access_mode": access_mode,
                    "updated_by": created_by or (current_user.id if current_user else None),
                    "app_id": app_id,
                    "tenant_id": tenant_id
                })

                permission_id = permission["id"]

                # 清除现有成员权限
                db.session.execute(text("""
                    DELETE FROM webapp_member_permissions_extend
                    WHERE webapp_permission_id = :permission_id
                """), {"permission_id": permission_id})
            else:
                # 创建新权限
                permission_id = str(uuid.uuid4())
                db.session.execute(text("""
                    INSERT INTO webapp_permissions_extend
                    (id, app_id, tenant_id, access_mode, created_by, created_at, updated_at)
                    VALUES (:id, :app_id, :tenant_id, :access_mode, :created_by, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """), {
                    "id": permission_id,
                    "app_id": app_id,
                    "tenant_id": tenant_id,
                    "access_mode": access_mode,
                    "created_by": created_by or (current_user.id if current_user else None)
                })

            # 如果是指定成员模式，添加成员权限
            if access_mode == WebAppAccessMode.PRIVATE and member_ids:
                for member_id in member_ids:
                    db.session.execute(text("""
                        INSERT INTO webapp_member_permissions_extend
                        (id, webapp_permission_id, account_id, subject_type, created_at)
                        VALUES (:id, :webapp_permission_id, :account_id, :subject_type, CURRENT_TIMESTAMP)
                    """), {
                        "id": str(uuid.uuid4()),
                        "webapp_permission_id": permission_id,
                        "account_id": member_id,
                        "subject_type": WebAppSubjectType.ACCOUNT
                    })

            db.session.commit()

            # 重新查询以获取完整数据
            return WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)

        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to create/update app permission: {str(e)}")
            raise

    @staticmethod
    def check_user_access_permission(app_id: str, tenant_id: str, user_id: str) -> bool:
        """检查用户是否有访问权限"""
        try:
            # 获取应用权限设置
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)

            if not permission:
                # 没有权限设置，默认允许访问
                return True

            access_mode = permission["access_mode"]

            # 公开访问
            if access_mode == WebAppAccessMode.PUBLIC:
                return True

            # 组织内所有成员访问
            if access_mode == WebAppAccessMode.PRIVATE_ALL:
                # 检查用户是否是该租户的成员
                from sqlalchemy import text
                result = db.session.execute(text("""
                    SELECT 1 FROM tenant_account_joins
                    WHERE tenant_id = :tenant_id AND account_id = :user_id
                """), {"tenant_id": tenant_id, "user_id": user_id})
                return result.fetchone() is not None

            # 指定成员访问
            if access_mode == WebAppAccessMode.PRIVATE:
                # 检查用户是否在指定成员列表中
                from sqlalchemy import text
                result = db.session.execute(text("""
                    SELECT 1 FROM webapp_member_permissions_extend
                    WHERE webapp_permission_id = :permission_id
                    AND account_id = :user_id
                    AND subject_type = :subject_type
                """), {
                    "permission_id": permission["id"],
                    "user_id": user_id,
                    "subject_type": WebAppSubjectType.ACCOUNT
                })
                return result.fetchone() is not None

            # SSO验证用户访问（暂时返回False，需要根据实际SSO实现）
            if access_mode == WebAppAccessMode.SSO_VERIFIED:
                return False

            return False

        except Exception as e:
            logger.error(f"Failed to check user access permission: {str(e)}")
            return False

    @staticmethod
    def get_app_access_mode(app_id: str, tenant_id: str) -> str:
        """获取应用访问模式"""
        try:
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)
            return permission["access_mode"] if permission else WebAppAccessMode.PUBLIC
        except Exception as e:
            logger.error(f"Failed to get app access mode: {str(e)}")
            return WebAppAccessMode.PUBLIC

    @staticmethod
    def search_members(tenant_id: str, keyword: str = "", limit: int = 20) -> List[Dict]:
        """搜索租户成员"""
        try:
            from sqlalchemy import text

            # 构建SQL查询
            sql = """
                SELECT a.id, a.name, a.email, a.avatar, taj.role
                FROM accounts a
                JOIN tenant_account_joins taj ON a.id = taj.account_id
                WHERE taj.tenant_id = :tenant_id
            """
            params = {"tenant_id": tenant_id, "limit": limit}

            if keyword:
                sql += " AND (a.name ILIKE :keyword OR a.email ILIKE :keyword)"
                params["keyword"] = f"%{keyword}%"

            sql += " ORDER BY a.name LIMIT :limit"

            result = db.session.execute(text(sql), params)

            members = []
            for row in result.fetchall():
                members.append({
                    "id": str(row[0]),
                    "name": row[1],
                    "email": row[2],
                    "avatar": row[3],
                    "avatarUrl": row[3],
                    "role": row[4]
                })

            return members

        except Exception as e:
            logger.error(f"Failed to search members: {str(e)}")
            return []

    @staticmethod
    def get_app_permission_with_members(app_id: str, tenant_id: str) -> Dict:
        """获取应用权限设置及成员信息"""
        try:
            permission = WebAppPermissionsServiceExtend.get_app_permission(app_id, tenant_id)

            if not permission:
                return {
                    "accessMode": WebAppAccessMode.PUBLIC,
                    "members": []
                }

            # 获取指定成员信息
            members = []
            if permission["access_mode"] == WebAppAccessMode.PRIVATE:
                from sqlalchemy import text
                result = db.session.execute(text("""
                    SELECT a.id, a.name, a.email, a.avatar
                    FROM accounts a
                    JOIN webapp_member_permissions_extend wmp ON a.id = wmp.account_id
                    WHERE wmp.webapp_permission_id = :permission_id
                    AND wmp.subject_type = :subject_type
                    ORDER BY a.name
                """), {
                    "permission_id": permission["id"],
                    "subject_type": WebAppSubjectType.ACCOUNT
                })

                for row in result.fetchall():
                    members.append({
                        "id": str(row[0]),
                        "name": row[1],
                        "email": row[2],
                        "avatar": row[3],
                        "avatarUrl": row[3]
                    })

            return {
                "accessMode": permission["access_mode"],
                "members": members
            }

        except Exception as e:
            logger.error(f"Failed to get app permission with members: {str(e)}")
            return {
                "accessMode": WebAppAccessMode.PUBLIC,
                "members": []
            }
