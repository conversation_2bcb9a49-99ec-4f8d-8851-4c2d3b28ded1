import logging

from flask_login import current_user
from flask_restful import Resource, reqparse
from werkzeug.exceptions import BadRequest, Forbidden

from controllers.console import api
from controllers.console.wraps import account_initialization_required, setup_required
from libs.login import login_required
from services.enterprise.enterprise_service import EnterpriseService
from services.webapp_permissions_service_extend import WebAppPermissionsServiceExtend

logger = logging.getLogger(__name__)


class WebAppAccessModeApiExtend(Resource):
    """WebApp访问模式API"""

    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """获取应用访问模式"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='args')
            args = parser.parse_args()

            app_id = args['appId']

            if not app_id:
                raise BadRequest("appId is required")

            try:
                # 使用新的权限服务获取应用访问模式
                access_mode = WebAppPermissionsServiceExtend.get_app_access_mode(
                    app_id=app_id,
                    tenant_id=current_user.current_tenant_id
                )
                return {
                    'accessMode': access_mode
                }, 200
            except Exception as e:
                # 应用不存在或其他错误时，返回默认访问模式
                logger.warning(f"Failed to get access mode for app {app_id}: {str(e)}, returning default 'public'")
                return {
                    'accessMode': 'public'  # 默认为公开访问
                }, 200

        except Exception as e:
            logger.error(f"Failed to get app access mode: {str(e)}", exc_info=True)
            # 即使出现异常，也返回默认访问模式而不是500错误
            return {
                'accessMode': 'public',  # 默认为公开访问
                'note': 'Default access mode returned due to error'
            }, 200
    
    @setup_required
    @login_required
    @account_initialization_required
    def post(self):
        """更新应用访问模式"""
        try:
            # 检查权限
            if not current_user.is_admin_or_owner:
                raise Forbidden("Only admin or owner can update app access mode")

            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='json')
            parser.add_argument('accessMode', type=str, required=True, location='json')
            parser.add_argument('subjects', type=list, required=False, location='json')
            args = parser.parse_args()

            app_id = args['appId']
            access_mode = args['accessMode']
            subjects = args.get('subjects', [])

            if access_mode not in ['public', 'private', 'private_all', 'sso_verified']:
                raise BadRequest("accessMode must be 'public', 'private', 'private_all', or 'sso_verified'")

            # 提取成员ID列表
            member_ids = []
            if access_mode == 'private' and subjects:
                member_ids = [
                    subject['subjectId'] for subject in subjects
                    if subject.get('subjectType') == 'account'
                ]

            # 使用新的权限服务更新应用访问模式
            permission = WebAppPermissionsServiceExtend.create_or_update_app_permission(
                app_id=app_id,
                tenant_id=current_user.current_tenant_id,
                access_mode=access_mode,
                member_ids=member_ids,
                created_by=current_user.id
            )

            return {
                'result': True,
                'accessMode': permission["access_mode"] if permission else access_mode
            }, 200

        except Exception as e:
            logger.error(f"Failed to update app access mode: {str(e)}", exc_info=True)
            return {'error': 'Failed to update app access mode'}, 500


class WebAppPermissionApiExtend(Resource):
    """WebApp权限API"""

    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """检查用户是否有权限访问应用"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='args')
            args = parser.parse_args()

            app_id = args['appId']

            if not app_id:
                raise BadRequest("appId is required")

            try:
                # 使用新的权限服务检查用户权限
                has_permission = WebAppPermissionsServiceExtend.check_user_access_permission(
                    app_id=app_id,
                    tenant_id=current_user.current_tenant_id,
                    user_id=current_user.id
                )
            except Exception as e:
                # 其他异常时，记录错误但允许访问
                logger.warning(f"Permission check failed for app {app_id}: {str(e)}, allowing access by default")
                has_permission = True

            return {
                'result': has_permission,
                'userId': str(current_user.id),
                'appId': app_id
            }, 200

        except Exception as e:
            logger.error(f"Failed to check app permission: {str(e)}", exc_info=True)
            # 即使出现异常，也返回默认权限而不是500错误
            return {
                'result': True,  # 默认允许访问
                'userId': str(current_user.id) if current_user else 'unknown',
                'appId': args.get('appId', 'unknown') if 'args' in locals() else 'unknown',
                'note': 'Default permission granted due to error'
            }, 200


class WebAppSubjectsApiExtend(Resource):
    """WebApp成员和组API"""

    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """获取应用权限设置的成员列表"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='args')
            args = parser.parse_args()

            app_id = args['appId']

            if not app_id:
                raise BadRequest("appId is required")

            # 获取应用权限设置及成员信息
            permission_data = WebAppPermissionsServiceExtend.get_app_permission_with_members(
                app_id=app_id,
                tenant_id=current_user.current_tenant_id
            )

            return {
                'groups': [],  # 暂时不支持组
                'members': permission_data['members']
            }, 200

        except Exception as e:
            logger.error(f"Failed to get app subjects: {str(e)}", exc_info=True)
            return {'error': 'Failed to get app subjects'}, 500


class WebAppMemberSearchApiExtend(Resource):
    """WebApp成员搜索API"""

    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """搜索租户成员"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('keyword', type=str, required=False, location='args', default='')
            parser.add_argument('limit', type=int, required=False, location='args', default=20)
            args = parser.parse_args()

            keyword = args.get('keyword', '')
            limit = min(args.get('limit', 20), 100)  # 限制最大返回数量

            # 搜索成员
            members = WebAppPermissionsServiceExtend.search_members(
                tenant_id=current_user.current_tenant_id,
                keyword=keyword,
                limit=limit
            )

            # 转换为前端期望的格式
            subjects = []
            for member in members:
                subjects.append({
                    'subjectId': member['id'],
                    'subjectType': 'account',
                    'accountData': {
                        'id': member['id'],
                        'name': member['name'],
                        'email': member['email'],
                        'avatar': member['avatar'],
                        'avatarUrl': member['avatarUrl']
                    }
                })

            return {
                'subjects': subjects,
                'hasMore': len(subjects) >= limit,
                'currPage': 1
            }, 200

        except Exception as e:
            logger.error(f"Failed to search members: {str(e)}", exc_info=True)
            return {'error': 'Failed to search members'}, 500


# 注册API路由 - 使用相对路径，因为blueprint已经有/console/api前缀
api.add_resource(WebAppAccessModeApiExtend, '/enterprise/webapp/app/access-mode')
api.add_resource(WebAppPermissionApiExtend, '/enterprise/webapp/permission')
api.add_resource(WebAppSubjectsApiExtend, '/enterprise/webapp/app/subjects')
api.add_resource(WebAppMemberSearchApiExtend, '/enterprise/webapp/members/search')