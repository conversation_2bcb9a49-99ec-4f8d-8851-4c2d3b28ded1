import logging

from flask_login import current_user
from flask_restful import Resource, reqparse
from werkzeug.exceptions import BadRequest, Forbidden

from controllers.console import api
from controllers.console.wraps import account_initialization_required, setup_required
from libs.login import login_required
from services.enterprise.enterprise_service import EnterpriseService

logger = logging.getLogger(__name__)


class WebAppAccessModeApiExtend(Resource):
    """WebApp访问模式API"""
    
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """获取应用访问模式"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='args')
            args = parser.parse_args()
            
            app_id = args['appId']
            
            if not app_id:
                raise BadRequest("appId is required")
            
            try:
                # 获取应用访问模式
                access_mode_settings = EnterpriseService.WebAppAuth.get_app_access_mode_by_id(app_id)
                return {
                    'accessMode': access_mode_settings.access_mode
                }, 200
            except Exception as e:
                # 应用不存在或其他错误时，返回默认访问模式
                logger.warning(f"Failed to get access mode for app {app_id}: {str(e)}, returning default 'public'")
                return {
                    'accessMode': 'public'  # 默认为公开访问
                }, 200
            
        except Exception as e:
            logger.error(f"Failed to get app access mode: {str(e)}", exc_info=True)
            # 即使出现异常，也返回默认访问模式而不是500错误
            return {
                'accessMode': 'public',  # 默认为公开访问
                'note': 'Default access mode returned due to error'
            }, 200
    
    @setup_required
    @login_required
    @account_initialization_required
    def post(self):
        """更新应用访问模式"""
        try:
            # 检查权限
            if not current_user.is_admin_or_owner:
                raise Forbidden("Only admin or owner can update app access mode")
            
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='json')
            parser.add_argument('accessMode', type=str, required=True, location='json')
            args = parser.parse_args()
            
            app_id = args['appId']
            access_mode = args['accessMode']
            
            if access_mode not in ['public', 'private', 'private_all']:
                raise BadRequest("accessMode must be 'public', 'private', or 'private_all'")
            
            # 更新应用访问模式
            result = EnterpriseService.WebAppAuth.update_app_access_mode(app_id, access_mode)
            
            return {
                'result': result,
                'accessMode': access_mode
            }, 200
            
        except Exception as e:
            logger.error(f"Failed to update app access mode: {str(e)}", exc_info=True)
            return {'error': 'Failed to update app access mode'}, 500


class WebAppPermissionApiExtend(Resource):
    """WebApp权限API"""
    
    @setup_required
    @login_required
    @account_initialization_required
    def get(self):
        """检查用户是否有权限访问应用"""
        try:
            parser = reqparse.RequestParser()
            parser.add_argument('appId', type=str, required=True, location='args')
            args = parser.parse_args()
            
            app_id = args['appId']
            
            if not app_id:
                raise BadRequest("appId is required")
            
            # 默认允许访问
            has_permission = True
            
            try:
                # 尝试获取应用代码
                from services.app_service import AppService
                app_code = AppService.get_app_code_by_id(app_id)
                
                # 检查用户权限
                has_permission = EnterpriseService.WebAppAuth.is_user_allowed_to_access_webapp(
                    user_id=str(current_user.id),
                    app_code=app_code
                )
            except ValueError as ve:
                # 应用不存在时，记录警告但允许访问
                logger.warning(f"App not found: {app_id}, allowing access by default")
                has_permission = True
            except Exception as e:
                # 其他异常时，记录错误但允许访问
                logger.warning(f"Permission check failed for app {app_id}: {str(e)}, allowing access by default")
                has_permission = True
            
            return {
                'result': has_permission,
                'userId': str(current_user.id),
                'appId': app_id
            }, 200
            
        except Exception as e:
            logger.error(f"Failed to check app permission: {str(e)}", exc_info=True)
            # 即使出现异常，也返回默认权限而不是500错误
            return {
                'result': True,  # 默认允许访问
                'userId': str(current_user.id) if current_user else 'unknown',
                'appId': args.get('appId', 'unknown') if 'args' in locals() else 'unknown',
                'note': 'Default permission granted due to error'
            }, 200


# 注册API路由 - 使用相对路径，因为blueprint已经有/console/api前缀
api.add_resource(WebAppAccessModeApiExtend, '/enterprise/webapp/app/access-mode')
api.add_resource(WebAppPermissionApiExtend, '/enterprise/webapp/permission') 